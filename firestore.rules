rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function hasRole(role) {
      return isAuthenticated() && 
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.roles != null &&
             role in get(/databases/$(database)/documents/users/$(request.auth.uid)).data.roles;
    }
    
    function isAdmin() {
      return hasRole('admin');
    }
    
    function isDriver() {
      return hasRole('driver');
    }
    
    function isOfficeManager() {
      return hasRole('office_manager');
    }
    
    function isSupport() {
      return hasRole('support');
    }
    
    // Users collection
    match /users/{userId} {
      // Users can read their own profile
      allow read: if isOwner(userId);
      
      // Users can create their own profile (only during registration)
      allow create: if isOwner(userId) && 
                       validateUserCreate(resource.data);
      
      // Users can update their own profile (restricted fields)
      allow update: if isOwner(userId) && 
                       validateUserUpdate(resource.data, request.auth.uid);
      
      // Admins and office managers can read all users
      allow read: if isAdmin() || isOfficeManager();
      
      // Admins can update user roles and status
      allow update: if isAdmin() && 
                       validateAdminUserUpdate(resource.data);
      
      // No deletion allowed (for audit purposes)
      allow delete: if false;
    }
    
    // Drivers collection
    match /drivers/{driverId} {
      // Drivers can read and update their own profile
      allow read, update: if isOwner(driverId) && isDriver();
      
      // Drivers can create their own driver profile
      allow create: if isOwner(driverId) && 
                       validateDriverCreate(resource.data);
      
      // Admins and office managers can read all drivers
      allow read: if isAdmin() || isOfficeManager();
      
      // Admins can update driver verification status
      allow update: if isAdmin() && 
                       validateDriverAdminUpdate(resource.data);
      
      // Office managers can read for assignment purposes
      allow read: if isOfficeManager();
    }
    
    // Orders collection
    match /orders/{orderId} {
      // Customers can read their own orders
      allow read: if isAuthenticated() && 
                     resource.data.customer.id == request.auth.uid;
      
      // Customers can create orders
      allow create: if isAuthenticated() && 
                       validateOrderCreate(resource.data);
      
      // Drivers can read orders assigned to them
      allow read: if isDriver() && 
                     resource.data.driver.id == request.auth.uid;
      
      // Drivers can update order status for their assigned orders
      allow update: if isDriver() && 
                       resource.data.driver.id == request.auth.uid &&
                       validateDriverOrderUpdate(resource.data);
      
      // Customers can update orders (limited fields before assignment)
      allow update: if isAuthenticated() && 
                       resource.data.customer.id == request.auth.uid &&
                       validateCustomerOrderUpdate(resource.data);
      
      // Admins and office managers can read and update all orders
      allow read, update: if isAdmin() || isOfficeManager();
      
      // System can create orders via Cloud Functions
      allow create: if request.auth.token.admin == true;
    }
    
    // Payments collection
    match /payments/{paymentId} {
      // Users can read their own payments
      allow read: if isAuthenticated() && 
                     resource.data.userId == request.auth.uid;
      
      // Only system (Cloud Functions) can create/update payments
      allow create, update: if request.auth.token.admin == true;
      
      // Admins can read all payments
      allow read: if isAdmin();
      
      // Office managers can read payments for reporting
      allow read: if isOfficeManager();
    }
    
    // Settings collection (admin only)
    match /settings/{settingId} {
      allow read, write: if isAdmin();
    }
    
    // Cities collection (read-only for authenticated users)
    match /cities/{cityId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // Categories collection (read-only for authenticated users)
    match /categories/{categoryId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // Notifications collection
    match /notifications/{notificationId} {
      // Users can read their own notifications
      allow read: if isAuthenticated() && 
                     resource.data.userId == request.auth.uid;
      
      // Users can update read status of their notifications
      allow update: if isAuthenticated() && 
                       resource.data.userId == request.auth.uid &&
                       validateNotificationUpdate(resource.data);
      
      // System can create notifications
      allow create: if request.auth.token.admin == true;
      
      // Admins can read all notifications
      allow read: if isAdmin();
    }
    
    // Analytics collection (admin and office manager only)
    match /analytics/{analyticsId} {
      allow read: if isAdmin() || isOfficeManager();
      allow write: if request.auth.token.admin == true;
    }
    
    // Reports collection (admin and office manager only)
    match /reports/{reportId} {
      allow read: if isAdmin() || isOfficeManager();
      allow write: if isAdmin();
    }
    
    // Support tickets collection
    match /support_tickets/{ticketId} {
      // Users can read their own tickets
      allow read: if isAuthenticated() && 
                     resource.data.userId == request.auth.uid;
      
      // Users can create support tickets
      allow create: if isAuthenticated() && 
                       validateTicketCreate(resource.data);
      
      // Support staff can read and update all tickets
      allow read, update: if isSupport() || isAdmin();
    }
    
    // Validation functions
    function validateUserCreate(data) {
      return data.keys().hasAll(['profile', 'roles', 'authentication', 'settings', 'stats', 'status']) &&
             data.profile.keys().hasAll(['firstName', 'lastName', 'phone', 'language']) &&
             data.roles is list &&
             data.roles.size() > 0 &&
             data.status in ['active', 'inactive'] &&
             data.profile.createdAt == request.time &&
             data.profile.updatedAt == request.time;
    }
    
    function validateUserUpdate(data, userId) {
      let allowedFields = ['profile', 'settings'];
      let restrictedProfileFields = ['createdAt'];
      
      return data.diff(resource.data).affectedKeys().hasOnly(allowedFields) &&
             (!data.diff(resource.data).affectedKeys().hasAny(['profile']) || 
              !data.profile.diff(resource.data.profile).affectedKeys().hasAny(restrictedProfileFields)) &&
             data.profile.updatedAt == request.time;
    }
    
    function validateAdminUserUpdate(data) {
      let allowedFields = ['roles', 'status', 'profile'];
      return data.diff(resource.data).affectedKeys().hasOnly(allowedFields);
    }
    
    function validateDriverCreate(data) {
      return data.keys().hasAll(['personalInfo', 'vehicle', 'documents', 'verification', 'location', 'status', 'earnings', 'schedule']) &&
             isDriver();
    }
    
    function validateDriverAdminUpdate(data) {
      let allowedFields = ['verification'];
      return data.diff(resource.data).affectedKeys().hasOnly(allowedFields) &&
             data.verification.keys().hasAll(['status']);
    }
    
    function validateOrderCreate(data) {
      return data.keys().hasAll(['customer', 'service', 'locations', 'pricing', 'status', 'payment', 'metadata']) &&
             data.customer.id == request.auth.uid &&
             data.status.current == 'pending' &&
             data.metadata.createdAt == request.time;
    }
    
    function validateDriverOrderUpdate(data) {
      let allowedFields = ['status', 'tracking', 'metadata'];
      return data.diff(resource.data).affectedKeys().hasOnly(allowedFields);
    }
    
    function validateCustomerOrderUpdate(data) {
      let allowedFields = ['locations', 'service', 'metadata'];
      let allowedStatuses = ['pending', 'cancelled'];
      
      return data.diff(resource.data).affectedKeys().hasOnly(allowedFields) &&
             data.status.current in allowedStatuses;
    }
    
    function validateNotificationUpdate(data) {
      let allowedFields = ['read'];
      return data.diff(resource.data).affectedKeys().hasOnly(allowedFields);
    }
    
    function validateTicketCreate(data) {
      return data.keys().hasAll(['userId', 'subject', 'description', 'status', 'priority']) &&
             data.userId == request.auth.uid &&
             data.status == 'open' &&
             data.createdAt == request.time;
    }
  }
}
