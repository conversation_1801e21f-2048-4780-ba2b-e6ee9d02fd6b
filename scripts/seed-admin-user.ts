#!/usr/bin/env tsx

/**
 * Admin User Seeding Script
 * 
 * This script creates an admin user <NAME_EMAIL>
 * It creates both the Firebase Authentication user and the Firestore document
 * 
 * Usage:
 * 1. Set environment variables (see .env.example)
 * 2. Run: npm run seed:admin
 * 3. Or run directly: npx tsx scripts/seed-admin-user.ts
 */

import { initializeApp, cert } from 'firebase-admin/app';
import { getAuth, Auth } from 'firebase-admin/auth';
import { getFirestore, Firestore, Timestamp } from 'firebase-admin/firestore';
import { readFileSync } from 'fs';
import { join } from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Admin user configuration
const ADMIN_USER_CONFIG = {
  email: '<EMAIL>',
  password: 'YellowTaxi2024!', // Strong password - change in production
  firstName: 'Admin',
  lastName: 'User',
  phone: '+962790000000', // Jordan phone number
  roles: ['admin'],
  profile: {
    firstName: 'Admin',
    lastName: 'User',
    phone: '+962790000000',
    email: '<EMAIL>',
    avatar: '',
    gender: 'male' as const,
    birthDate: '1990-01-01',
    language: 'en' as const,
  }
};

interface AdminUserData {
  profile: {
    firstName: string;
    lastName: string;
    phone: string;
    email: string;
    avatar: string;
    gender: 'male' | 'female';
    birthDate: string;
    language: 'en' | 'ar';
    createdAt: Timestamp;
    updatedAt: Timestamp;
  };
  roles: string[];
  authentication: {
    phoneVerified: boolean;
    emailVerified: boolean;
    providers: string[];
    lastLogin: Timestamp;
    activeUntil: Timestamp;
  };
  settings: {
    notifications: {
      orderUpdates: boolean;
      promotions: boolean;
      system: boolean;
    };
    privacy: {
      shareLocation: boolean;
      showProfile: boolean;
    };
    theme: 'light' | 'dark';
  };
  stats: {
    totalOrders: number;
    totalSpent: number;
    averageRating: number;
    joinedAt: Timestamp;
  };
  status: 'active' | 'inactive' | 'suspended';
}

class AdminUserSeeder {
  private auth!: Auth;
  private db!: Firestore;
  private isInitialized = false;

  constructor() {
    this.initializeFirebaseAdmin();
  }

  private initializeFirebaseAdmin(): void {
    try {
      // Check if Firebase Admin is already initialized
      if (this.isInitialized) {
        return;
      }

      // Get service account key path
      const serviceAccountPath = process.env.FIREBASE_SERVICE_ACCOUNT_PATH;
      
      if (!serviceAccountPath) {
        throw new Error('FIREBASE_SERVICE_ACCOUNT_PATH environment variable is required');
      }

      // Read service account key
      const serviceAccount = JSON.parse(readFileSync(serviceAccountPath, 'utf8'));

      // Initialize Firebase Admin
      initializeApp({
        credential: cert(serviceAccount),
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
      });

      this.auth = getAuth();
      this.db = getFirestore();
      this.isInitialized = true;

      console.log('✅ Firebase Admin initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Firebase Admin:', error);
      throw error;
    }
  }

  async seedAdminUser(): Promise<void> {
    try {
      console.log('🚀 Starting admin user seeding process...');
      console.log(`📧 Email: ${ADMIN_USER_CONFIG.email}`);
      console.log(`📱 Phone: ${ADMIN_USER_CONFIG.phone}`);

      // Check if user already exists
      const existingUser = await this.checkExistingUser();
      
      if (existingUser) {
        console.log('⚠️  Admin user already exists, updating roles and profile...');
        await this.updateExistingUser(existingUser.uid);
      } else {
        console.log('🆕 Creating new admin user...');
        await this.createNewAdminUser();
      }

      console.log('✅ Admin user seeding completed successfully!');
      console.log('🔑 Login credentials:');
      console.log(`   Email: ${ADMIN_USER_CONFIG.email}`);
      console.log(`   Password: ${ADMIN_USER_CONFIG.password}`);
      console.log('⚠️  Remember to change the password after first login!');

    } catch (error) {
      console.error('❌ Admin user seeding failed:', error);
      throw error;
    }
  }

  private async checkExistingUser(): Promise<import('firebase-admin/auth').UserRecord | null> {
    try {
      // Try to get user by email
      const userRecord = await this.auth.getUserByEmail(ADMIN_USER_CONFIG.email);
      return userRecord;
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'code' in error && error.code === 'auth/user-not-found') {
        return null;
      }
      throw error;
    }
  }

  private async createNewAdminUser(): Promise<void> {
    try {
      // Create user in Firebase Auth
      console.log('🔐 Creating Firebase Auth user...');
      const userRecord = await this.auth.createUser({
        email: ADMIN_USER_CONFIG.email,
        password: ADMIN_USER_CONFIG.password,
        displayName: `${ADMIN_USER_CONFIG.firstName} ${ADMIN_USER_CONFIG.lastName}`,
        phoneNumber: ADMIN_USER_CONFIG.phone,
        emailVerified: true,
        disabled: false,
      });

      console.log(`✅ Firebase Auth user created with UID: ${userRecord.uid}`);

      // Create user document in Firestore
      console.log('📄 Creating Firestore user document...');
      await this.createUserDocument(userRecord.uid);

      // Set custom claims for admin role
      console.log('🔑 Setting admin custom claims...');
      await this.auth.setCustomUserClaims(userRecord.uid, {
        roles: ADMIN_USER_CONFIG.roles,
        isAdmin: true,
        permissions: ['*'] // All permissions for admin
      });

      console.log('✅ Admin user creation completed successfully');

    } catch (error) {
      console.error('❌ Failed to create admin user:', error);
      throw error;
    }
  }

  private async updateExistingUser(uid: string): Promise<void> {
    try {
      // Update user profile in Firebase Auth
      console.log('🔄 Updating existing user profile...');
      await this.auth.updateUser(uid, {
        displayName: `${ADMIN_USER_CONFIG.firstName} ${ADMIN_USER_CONFIG.lastName}`,
        phoneNumber: ADMIN_USER_CONFIG.phone,
        emailVerified: true,
      });

      // Update or create user document in Firestore
      console.log('📄 Updating Firestore user document...');
      await this.createUserDocument(uid);

      // Update custom claims
      console.log('🔑 Updating admin custom claims...');
      await this.auth.setCustomUserClaims(uid, {
        roles: ADMIN_USER_CONFIG.roles,
        isAdmin: true,
        permissions: ['*']
      });

      console.log('✅ Existing user updated successfully');

    } catch (error) {
      console.error('❌ Failed to update existing user:', error);
      throw error;
    }
  }

  private async createUserDocument(uid: string): Promise<void> {
    try {
      const now = Timestamp.now();
      const userData: AdminUserData = {
        profile: {
          ...ADMIN_USER_CONFIG.profile,
          createdAt: now,
          updatedAt: now,
        },
        roles: ADMIN_USER_CONFIG.roles,
        authentication: {
          phoneVerified: true,
          emailVerified: true,
          providers: ['email'],
          lastLogin: now,
          activeUntil: Timestamp.fromDate(new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)), // 1 year
        },
        settings: {
          notifications: {
            orderUpdates: true,
            promotions: true,
            system: true,
          },
          privacy: {
            shareLocation: true,
            showProfile: true,
          },
          theme: 'light',
        },
        stats: {
          totalOrders: 0,
          totalSpent: 0,
          averageRating: 5,
          joinedAt: now,
        },
        status: 'active',
      };

      // Set document with merge to avoid overwriting existing data
      await this.db.collection('users').doc(uid).set(userData, { merge: true });
      console.log('✅ Firestore user document created/updated successfully');

    } catch (error) {
      console.error('❌ Failed to create/update Firestore document:', error);
      throw error;
    }
  }

  async verifyAdminUser(): Promise<void> {
    try {
      console.log('🔍 Verifying admin user setup...');
      
      const userRecord = await this.auth.getUserByEmail(ADMIN_USER_CONFIG.email);
      console.log(`✅ Firebase Auth user verified: ${userRecord.uid}`);

      const userDoc = await this.db.collection('users').doc(userRecord.uid).get();
      if (userDoc.exists) {
        console.log('✅ Firestore user document verified');
        const userData = userDoc.data();
        console.log(`   Roles: ${userData?.roles?.join(', ')}`);
        console.log(`   Status: ${userData?.status}`);
      } else {
        console.log('❌ Firestore user document not found');
      }

      const customClaims = userRecord.customClaims;
      if (customClaims?.isAdmin) {
        console.log('✅ Admin custom claims verified');
        console.log(`   Permissions: ${customClaims.permissions?.join(', ')}`);
      } else {
        console.log('❌ Admin custom claims not found');
      }

    } catch (error) {
      console.error('❌ Admin user verification failed:', error);
      throw error;
    }
  }
}

// Main execution function
async function main(): Promise<void> {
  try {
    console.log('🚕 YellowTaxi Admin User Seeding Script');
    console.log('=====================================\n');

    // Validate environment variables
    const requiredEnvVars = [
      'FIREBASE_SERVICE_ACCOUNT_PATH',
      'NEXT_PUBLIC_FIREBASE_PROJECT_ID'
    ];

    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    if (missingEnvVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
    }

    const seeder = new AdminUserSeeder();
    
    // Seed the admin user
    await seeder.seedAdminUser();
    
    console.log('\n🔍 Verifying setup...');
    await seeder.verifyAdminUser();
    
    console.log('\n🎉 Admin user seeding completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Test login with the admin account');
    console.log('   2. Change the default password');
    console.log('   3. Configure additional admin permissions if needed');
    console.log('   4. Set up admin dashboard access');

  } catch (error) {
    console.error('\n❌ Seeding failed:', error);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { AdminUserSeeder, main };
