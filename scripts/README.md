# YellowTaxi User Seeding Scripts

This directory contains comprehensive user seeding scripts for the YellowTaxi platform. These scripts create users with different roles and appropriate permissions in both Firebase Authentication and Firestore.

## 🚀 Available Scripts

### 1. Admin User Seeding (`seed-admin-user.ts`)
Creates an admin user with full system permissions.

**Email**: `<EMAIL>`  
**Password**: `YellowTaxi2024!`  
**Role**: `admin`  
**Permissions**: All system permissions

### 2. Customer User Seeding (`seed-customer-user.ts`)
Creates a customer user with order management permissions.

**Email**: `<EMAIL>`  
**Password**: `Customer2024!`  
**Role**: `customer`  
**Permissions**: Create orders, view profile, edit profile, view orders

### 3. Driver User Seeding (`seed-driver-user.ts`)
Creates a driver user with ride acceptance permissions.

**Email**: `<EMAIL>`  
**Password**: `Driver2024!`  
**Role**: `driver`  
**Permissions**: Accept orders, view profile, edit profile, view earnings, update location

## 📋 Prerequisites

Before running any seeding script, ensure you have:

1. **Firebase Project Setup**
   - Firebase project created and configured
   - Service account key file downloaded
   - Firestore database enabled

2. **Environment Variables**
   Create a `.env.local` file with:
   ```bash
   FIREBASE_SERVICE_ACCOUNT_PATH=/path/to/your/serviceAccountKey.json
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
   ```

3. **Dependencies Installed**
   ```bash
   npm install
   ```

## 🛠️ Usage

### Individual Scripts

```bash
# Seed admin user only
npm run seed:admin

# Seed customer user only
npm run seed:customer

# Seed driver user only
npm run seed:driver
```

### Seed All Users

```bash
# Seed all three user types at once
npm run seed:all
```

### Direct Execution

```bash
# Using npx
npx tsx scripts/seed-admin-user.ts
npx tsx scripts/seed-customer-user.ts
npx tsx scripts/seed-driver-user.ts

# Using tsx directly
tsx scripts/seed-admin-user.ts
tsx scripts/seed-customer-user.ts
tsx scripts/seed-driver-user.ts
```

## 🔧 Script Features

### Common Features (All Scripts)
- ✅ **Firebase Admin Initialization**: Proper Firebase Admin SDK setup
- ✅ **User Creation**: Creates Firebase Auth user with verified email
- ✅ **Firestore Document**: Creates comprehensive user document
- ✅ **Custom Claims**: Sets appropriate role-based permissions
- ✅ **Error Handling**: Comprehensive error handling and logging
- ✅ **Verification**: Verifies user creation and setup
- ✅ **Idempotent**: Safe to run multiple times (updates existing users)

### Admin-Specific Features
- 🔑 Full system permissions (`*`)
- 👑 Admin role assignment
- 📊 System management capabilities

### Customer-Specific Features
- 🏠 Home and work address configuration
- 🚗 Vehicle preferences (economy/comfort/premium)
- 📱 Emergency contact information
- 💳 Payment method preferences
- 🚶‍♂️ Pooling and wait time preferences

### Driver-Specific Features
- 🚗 Comprehensive vehicle information
- 📄 Document management (license, insurance, registration)
- ✅ Verification status tracking
- 📍 Location and availability management
- 💰 Earnings and performance tracking
- ⏰ Working hours and area preferences

## 📊 User Data Structure

### Base User Fields (All Users)
```typescript
{
  profile: { firstName, lastName, phone, email, avatar, gender, birthDate, language },
  roles: string[],
  authentication: { phoneVerified, emailVerified, providers, lastLogin, activeUntil },
  settings: { notifications, privacy, theme },
  stats: { totalOrders, totalSpent, averageRating, joinedAt },
  status: 'active' | 'inactive' | 'suspended'
}
```

### Customer-Specific Fields
```typescript
{
  customer: {
    preferredPaymentMethod,
    homeAddress: { street, city, district, coordinates },
    workAddress: { street, city, district, coordinates },
    emergencyContact: { name, phone, relationship },
    preferences: { vehicleType, maxWaitTime, allowPooling, preferredDrivers }
  }
}
```

### Driver-Specific Fields
```typescript
{
  driver: {
    driverId, licenseNumber, licenseExpiry,
    vehicleInfo: { make, model, year, color, plateNumber, vehicleType, capacity, features },
    documents: { license, insurance, registration, inspection },
    verification: { identityVerified, licenseVerified, vehicleVerified, backgroundCheck, drugTest },
    location: { current, home },
    availability: { isOnline, workingHours, maxDistance, preferredAreas },
    earnings: { totalEarnings, thisWeek, thisMonth, averagePerRide, paymentMethod, bankInfo },
    performance: { rating, totalRides, completedRides, cancelledRides, customerComplaints },
    preferences: { acceptPooling, acceptLongDistance, acceptNightRides, preferredPaymentMethod, maxWaitTime }
  }
}
```

## 🔐 Security Features

- **Strong Passwords**: All users get strong, unique passwords
- **Role-Based Access**: Proper permission assignment based on user type
- **Custom Claims**: Firebase Auth custom claims for role verification
- **Document Security**: Firestore security rules protect user data
- **Verification**: Email and phone verification enabled by default

## 🚨 Important Notes

### Production Considerations
1. **Change Default Passwords**: All users should change passwords after first login
2. **Document URLs**: Driver document URLs are placeholders - update with real URLs
3. **Coordinates**: Address coordinates are examples - update with real locations
4. **Bank Information**: Driver bank details are examples - update with real information

### Data Privacy
- All scripts create users with `emailVerified: true` and `phoneVerified: true`
- Personal information is structured for taxi service requirements
- Emergency contacts and addresses are included for safety

### Error Handling
- Scripts handle existing users gracefully (update mode)
- Comprehensive logging for debugging
- Proper error messages for missing environment variables

## 🔍 Verification

Each script includes a verification step that checks:
- ✅ Firebase Auth user creation
- ✅ Firestore document creation
- ✅ Custom claims assignment
- ✅ Role and permission verification

## 📝 Customization

### Modifying User Data
Edit the `*_USER_CONFIG` constants in each script to customize:
- User names and contact information
- Addresses and coordinates
- Vehicle information (for drivers)
- Preferences and settings

### Adding New Fields
Extend the TypeScript interfaces to add new fields:
1. Update the interface (e.g., `CustomerUserData`)
2. Add data to the `createUserDocument` method
3. Update verification if needed

## 🆘 Troubleshooting

### Common Issues

1. **Environment Variables Missing**
   ```
   Error: Missing required environment variables: FIREBASE_SERVICE_ACCOUNT_PATH
   ```
   **Solution**: Create `.env.local` file with required variables

2. **Service Account Path Invalid**
   ```
   Error: ENOENT: no such file or directory
   ```
   **Solution**: Verify the path to your Firebase service account key

3. **Firebase Project ID Mismatch**
   ```
   Error: Project not found
   ```
   **Solution**: Check `NEXT_PUBLIC_FIREBASE_PROJECT_ID` in environment

4. **Permission Denied**
   ```
   Error: Permission denied
   ```
   **Solution**: Ensure service account has proper Firebase Admin permissions

### Debug Mode
Add `console.log` statements in the scripts to debug specific issues:
```typescript
console.log('Debug: Service account path:', serviceAccountPath);
console.log('Debug: Project ID:', process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID);
```

## 📚 Related Documentation

- [Firebase Admin SDK Documentation](https://firebase.google.com/docs/admin)
- [Firestore Security Rules](https://firebase.google.com/docs/firestore/security/get-started)
- [Firebase Authentication](https://firebase.google.com/docs/auth)
- [YellowTaxi Project Documentation](../README.md)

## 🤝 Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify environment variables and Firebase configuration
3. Review Firebase console for authentication and database errors
4. Check script logs for detailed error information

---

**Note**: These scripts are designed for development and testing environments. For production use, ensure proper security measures and customize user data according to your requirements.
