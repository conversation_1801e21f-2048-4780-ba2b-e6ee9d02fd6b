#!/usr/bin/env tsx

/**
 * Customer User Seeding Script
 * 
 * This script creates a customer user <NAME_EMAIL>
 * It creates both the Firebase Authentication user and the Firestore document
 * 
 * Usage:
 * 1. Set environment variables (see .env.example)
 * 2. Run: npm run seed:customer
 * 3. Or run directly: npx tsx scripts/seed-customer-user.ts
 */

import { initializeApp, cert } from 'firebase-admin/app';
import { getAuth, Auth } from 'firebase-admin/auth';
import { getFirestore, Firestore, Timestamp } from 'firebase-admin/firestore';
import { readFileSync } from 'fs';
import { join } from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Customer user configuration
const CUSTOMER_USER_CONFIG = {
  email: '<EMAIL>',
  password: 'Customer2024!', // Strong password - change in production
  firstName: '<PERSON>',
  lastName: 'Customer',
  phone: '+962791234567', // Jordan phone number
  roles: ['customer'],
  profile: {
    firstName: '<PERSON>',
    lastName: 'Customer',
    phone: '+962791234567',
    email: '<EMAIL>',
    avatar: '',
    gender: 'male' as const,
    birthDate: '1995-05-15',
    language: 'en' as const,
  }
};

interface CustomerUserData {
  profile: {
    firstName: string;
    lastName: string;
    phone: string;
    email: string;
    avatar: string;
    gender: 'male' | 'female';
    birthDate: string;
    language: 'en' | 'ar';
    createdAt: Timestamp;
    updatedAt: Timestamp;
  };
  roles: string[];
  authentication: {
    phoneVerified: boolean;
    emailVerified: boolean;
    providers: string[];
    lastLogin: Timestamp;
    activeUntil: Timestamp;
  };
  settings: {
    notifications: {
      orderUpdates: boolean;
      promotions: boolean;
      system: boolean;
    };
    privacy: {
      shareLocation: boolean;
      showProfile: boolean;
    };
    theme: 'light' | 'dark';
  };
  stats: {
    totalOrders: number;
    totalSpent: number;
    averageRating: number;
    joinedAt: Timestamp;
  };
  status: 'active' | 'inactive' | 'suspended';
  // Customer-specific fields
  customer: {
    preferredPaymentMethod: 'cash' | 'card' | 'wallet';
    homeAddress: {
      street: string;
      city: string;
      district: string;
      coordinates: {
        lat: number;
        lng: number;
      };
    };
    workAddress: {
      street: string;
      city: string;
      district: string;
      coordinates: {
        lat: number;
        lng: number;
      };
    };
    emergencyContact: {
      name: string;
      phone: string;
      relationship: string;
    };
    preferences: {
      vehicleType: 'economy' | 'comfort' | 'premium';
      maxWaitTime: number; // minutes
      allowPooling: boolean;
      preferredDrivers: string[];
    };
  };
}

class CustomerUserSeeder {
  private auth!: Auth;
  private db!: Firestore;
  private isInitialized = false;

  constructor() {
    this.initializeFirebaseAdmin();
  }

  private initializeFirebaseAdmin(): void {
    try {
      // Check if Firebase Admin is already initialized
      if (this.isInitialized) {
        return;
      }

      // Get service account key path
      const serviceAccountPath = process.env.FIREBASE_SERVICE_ACCOUNT_PATH;
      
      if (!serviceAccountPath) {
        throw new Error('FIREBASE_SERVICE_ACCOUNT_PATH environment variable is required');
      }

      // Read service account key
      const serviceAccount = JSON.parse(readFileSync(serviceAccountPath, 'utf8'));

      // Initialize Firebase Admin
      initializeApp({
        credential: cert(serviceAccount),
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
      });

      this.auth = getAuth();
      this.db = getFirestore();
      this.isInitialized = true;

      console.log('✅ Firebase Admin initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Firebase Admin:', error);
      throw error;
    }
  }

  async seedCustomerUser(): Promise<void> {
    try {
      console.log('🚀 Starting customer user seeding process...');
      console.log(`📧 Email: ${CUSTOMER_USER_CONFIG.email}`);
      console.log(`📱 Phone: ${CUSTOMER_USER_CONFIG.phone}`);

      // Check if user already exists
      const existingUser = await this.checkExistingUser();
      
      if (existingUser) {
        console.log('⚠️  Customer user already exists, updating roles and profile...');
        await this.updateExistingUser(existingUser.uid);
      } else {
        console.log('🆕 Creating new customer user...');
        await this.createNewCustomerUser();
      }

      console.log('✅ Customer user seeding completed successfully!');
      console.log('🔑 Login credentials:');
      console.log(`   Email: ${CUSTOMER_USER_CONFIG.email}`);
      console.log(`   Password: ${CUSTOMER_USER_CONFIG.password}`);
      console.log('⚠️  Remember to change the password after first login!');

    } catch (error) {
      console.error('❌ Customer user seeding failed:', error);
      throw error;
    }
  }

  private async checkExistingUser(): Promise<import('firebase-admin/auth').UserRecord | null> {
    try {
      // Try to get user by email
      const userRecord = await this.auth.getUserByEmail(CUSTOMER_USER_CONFIG.email);
      return userRecord;
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'code' in error && error.code === 'auth/user-not-found') {
        return null;
      }
      throw error;
    }
  }

  private async createNewCustomerUser(): Promise<void> {
    try {
      // Create user in Firebase Auth
      console.log('🔐 Creating Firebase Auth user...');
      const userRecord = await this.auth.createUser({
        email: CUSTOMER_USER_CONFIG.email,
        password: CUSTOMER_USER_CONFIG.password,
        displayName: `${CUSTOMER_USER_CONFIG.firstName} ${CUSTOMER_USER_CONFIG.lastName}`,
        phoneNumber: CUSTOMER_USER_CONFIG.phone,
        emailVerified: true,
        disabled: false,
      });

      console.log(`✅ Firebase Auth user created with UID: ${userRecord.uid}`);

      // Create user document in Firestore
      console.log('📄 Creating Firestore user document...');
      await this.createUserDocument(userRecord.uid);

      // Set custom claims for customer role
      console.log('🔑 Setting customer custom claims...');
      await this.auth.setCustomUserClaims(userRecord.uid, {
        roles: CUSTOMER_USER_CONFIG.roles,
        isCustomer: true,
        permissions: ['create_order', 'view_profile', 'edit_profile', 'view_orders']
      });

      console.log('✅ Customer user creation completed successfully');

    } catch (error) {
      console.error('❌ Failed to create customer user:', error);
      throw error;
    }
  }

  private async updateExistingUser(uid: string): Promise<void> {
    try {
      // Update user profile in Firebase Auth
      console.log('🔄 Updating existing user profile...');
      await this.auth.updateUser(uid, {
        displayName: `${CUSTOMER_USER_CONFIG.firstName} ${CUSTOMER_USER_CONFIG.lastName}`,
        phoneNumber: CUSTOMER_USER_CONFIG.phone,
        emailVerified: true,
      });

      // Update or create user document in Firestore
      console.log('📄 Updating Firestore user document...');
      await this.createUserDocument(uid);

      // Update custom claims
      console.log('🔑 Updating customer custom claims...');
      await this.auth.setCustomUserClaims(uid, {
        roles: CUSTOMER_USER_CONFIG.roles,
        isCustomer: true,
        permissions: ['create_order', 'view_profile', 'edit_profile', 'view_orders']
      });

      console.log('✅ Existing user updated successfully');

    } catch (error) {
      console.error('❌ Failed to update existing user:', error);
      throw error;
    }
  }

  private async createUserDocument(uid: string): Promise<void> {
    try {
      const now = Timestamp.now();
      const userData: CustomerUserData = {
        profile: {
          ...CUSTOMER_USER_CONFIG.profile,
          createdAt: now,
          updatedAt: now,
        },
        roles: CUSTOMER_USER_CONFIG.roles,
        authentication: {
          phoneVerified: true,
          emailVerified: true,
          providers: ['email'],
          lastLogin: now,
          activeUntil: Timestamp.fromDate(new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)), // 1 year
        },
        settings: {
          notifications: {
            orderUpdates: true,
            promotions: true,
            system: true,
          },
          privacy: {
            shareLocation: true,
            showProfile: true,
          },
          theme: 'light',
        },
        stats: {
          totalOrders: 0,
          totalSpent: 0,
          averageRating: 5,
          joinedAt: now,
        },
        status: 'active',
        customer: {
          preferredPaymentMethod: 'cash',
          homeAddress: {
            street: 'King Hussein Street',
            city: 'Amman',
            district: 'Abdali',
            coordinates: {
              lat: 31.9539,
              lng: 35.9106,
            },
          },
          workAddress: {
            street: 'Al-Rainbow Street',
            city: 'Amman',
            district: 'Jabal Amman',
            coordinates: {
              lat: 31.9454,
              lng: 35.9284,
            },
          },
          emergencyContact: {
            name: 'Jane Customer',
            phone: '+************',
            relationship: 'Spouse',
          },
          preferences: {
            vehicleType: 'comfort',
            maxWaitTime: 15,
            allowPooling: false,
            preferredDrivers: [],
          },
        },
      };

      // Set document with merge to avoid overwriting existing data
      await this.db.collection('users').doc(uid).set(userData, { merge: true });
      console.log('✅ Firestore user document created/updated successfully');

    } catch (error) {
      console.error('❌ Failed to create/update Firestore document:', error);
      throw error;
    }
  }

  async verifyCustomerUser(): Promise<void> {
    try {
      console.log('🔍 Verifying customer user setup...');
      
      const userRecord = await this.auth.getUserByEmail(CUSTOMER_USER_CONFIG.email);
      console.log(`✅ Firebase Auth user verified: ${userRecord.uid}`);

      const userDoc = await this.db.collection('users').doc(userRecord.uid).get();
      if (userDoc.exists) {
        console.log('✅ Firestore user document verified');
        const userData = userDoc.data();
        console.log(`   Roles: ${userData?.roles?.join(', ')}`);
        console.log(`   Status: ${userData?.status}`);
        console.log(`   Customer Type: ${userData?.customer?.preferredVehicleType || 'Not set'}`);
      } else {
        console.log('❌ Firestore user document not found');
      }

      const customClaims = userRecord.customClaims;
      if (customClaims?.isCustomer) {
        console.log('✅ Customer custom claims verified');
        console.log(`   Permissions: ${customClaims.permissions?.join(', ')}`);
      } else {
        console.log('❌ Customer custom claims not found');
      }

    } catch (error) {
      console.error('❌ Customer user verification failed:', error);
      throw error;
    }
  }
}

// Main execution function
async function main(): Promise<void> {
  try {
    console.log('🚕 YellowTaxi Customer User Seeding Script');
    console.log('========================================\n');

    // Validate environment variables
    const requiredEnvVars = [
      'FIREBASE_SERVICE_ACCOUNT_PATH',
      'NEXT_PUBLIC_FIREBASE_PROJECT_ID'
    ];

    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    if (missingEnvVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
    }

    const seeder = new CustomerUserSeeder();
    
    // Seed the customer user
    await seeder.seedCustomerUser();
    
    console.log('\n🔍 Verifying setup...');
    await seeder.verifyCustomerUser();
    
    console.log('\n🎉 Customer user seeding completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Test login with the customer account');
    console.log('   2. Change the default password');
    console.log('   3. Configure customer preferences and addresses');
    console.log('   4. Test customer dashboard access');

  } catch (error) {
    console.error('\n❌ Seeding failed:', error);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { CustomerUserSeeder, main };
