#!/usr/bin/env tsx

/**
 * Driver User Seeding Script
 * 
 * This script creates a driver user <NAME_EMAIL>
 * It creates both the Firebase Authentication user and the Firestore document
 * 
 * Usage:
 * 1. Set environment variables (see .env.example)
 * 2. Run: npm run seed:driver
 * 3. Or run directly: npx tsx scripts/seed-driver-user.ts
 */

import { initializeApp, cert } from 'firebase-admin/app';
import { getAuth, Auth } from 'firebase-admin/auth';
import { getFirestore, Firestore, Timestamp } from 'firebase-admin/firestore';
import { readFileSync } from 'fs';
import { join } from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Driver user configuration
const DRIVER_USER_CONFIG = {
  email: '<EMAIL>',
  password: 'Driver2024!', // Strong password - change in production
  firstName: '<PERSON>',
  lastName: 'Driver',
  phone: '+962792345678', // Jordan phone number
  roles: ['driver'],
  profile: {
    firstName: '<PERSON>',
    lastName: 'Driver',
    phone: '+962792345678',
    email: '<EMAIL>',
    avatar: '',
    gender: 'male' as const,
    birthDate: '1988-08-20',
    language: 'ar' as const,
  }
};

interface DriverUserData {
  profile: {
    firstName: string;
    lastName: string;
    phone: string;
    email: string;
    avatar: string;
    gender: 'male' | 'female';
    birthDate: string;
    language: 'en' | 'ar';
    createdAt: Timestamp;
    updatedAt: Timestamp;
  };
  roles: string[];
  authentication: {
    phoneVerified: boolean;
    emailVerified: boolean;
    providers: string[];
    lastLogin: Timestamp;
    activeUntil: Timestamp;
  };
  settings: {
    notifications: {
      orderUpdates: boolean;
      promotions: boolean;
      system: boolean;
    };
    privacy: {
      shareLocation: boolean;
      showProfile: boolean;
    };
    theme: 'light' | 'dark';
  };
  stats: {
    totalOrders: number;
    totalSpent: number;
    averageRating: number;
    joinedAt: Timestamp;
  };
  status: 'active' | 'inactive' | 'suspended';
  // Driver-specific fields
  driver: {
    driverId: string;
    licenseNumber: string;
    licenseExpiry: string;
    vehicleInfo: {
      make: string;
      model: string;
      year: number;
      color: string;
      plateNumber: string;
      vehicleType: 'economy' | 'comfort' | 'premium';
      capacity: number;
      features: string[];
    };
    documents: {
      license: string;
      insurance: string;
      registration: string;
      inspection: string;
    };
    verification: {
      identityVerified: boolean;
      licenseVerified: boolean;
      vehicleVerified: boolean;
      backgroundCheck: boolean;
      drugTest: boolean;
      verifiedAt: Timestamp;
    };
    location: {
      current: {
        lat: number;
        lng: number;
        updatedAt: Timestamp;
      };
      home: {
        lat: number;
        lng: number;
        address: string;
      };
    };
    availability: {
      isOnline: boolean;
      workingHours: {
        start: string;
        end: string;
        days: string[];
      };
      maxDistance: number; // km
      preferredAreas: string[];
    };
    earnings: {
      totalEarnings: number;
      thisWeek: number;
      thisMonth: number;
      averagePerRide: number;
      paymentMethod: 'bank' | 'cash' | 'wallet';
      bankInfo: {
        bankName: string;
        accountNumber: string;
        accountHolder: string;
      };
    };
    performance: {
      rating: number;
      totalRides: number;
      completedRides: number;
      cancelledRides: number;
      customerComplaints: number;
      lastRatingUpdate: Timestamp;
    };
    preferences: {
      acceptPooling: boolean;
      acceptLongDistance: boolean;
      acceptNightRides: boolean;
      preferredPaymentMethod: 'cash' | 'card' | 'wallet';
      maxWaitTime: number; // minutes
    };
  };
}

class DriverUserSeeder {
  private auth!: Auth;
  private db!: Firestore;
  private isInitialized = false;

  constructor() {
    this.initializeFirebaseAdmin();
  }

  private initializeFirebaseAdmin(): void {
    try {
      // Check if Firebase Admin is already initialized
      if (this.isInitialized) {
        return;
      }

      // Get service account key path
      const serviceAccountPath = process.env.FIREBASE_SERVICE_ACCOUNT_PATH;
      
      if (!serviceAccountPath) {
        throw new Error('FIREBASE_SERVICE_ACCOUNT_PATH environment variable is required');
      }

      // Read service account key
      const serviceAccount = JSON.parse(readFileSync(serviceAccountPath, 'utf8'));

      // Initialize Firebase Admin
      initializeApp({
        credential: cert(serviceAccount),
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
      });

      this.auth = getAuth();
      this.db = getFirestore();
      this.isInitialized = true;

      console.log('✅ Firebase Admin initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Firebase Admin:', error);
      throw error;
    }
  }

  async seedDriverUser(): Promise<void> {
    try {
      console.log('🚀 Starting driver user seeding process...');
      console.log(`📧 Email: ${DRIVER_USER_CONFIG.email}`);
      console.log(`📱 Phone: ${DRIVER_USER_CONFIG.phone}`);

      // Check if user already exists
      const existingUser = await this.checkExistingUser();
      
      if (existingUser) {
        console.log('⚠️  Driver user already exists, updating roles and profile...');
        await this.updateExistingUser(existingUser.uid);
      } else {
        console.log('🆕 Creating new driver user...');
        await this.createNewDriverUser();
      }

      console.log('✅ Driver user seeding completed successfully!');
      console.log('🔑 Login credentials:');
      console.log(`   Email: ${DRIVER_USER_CONFIG.email}`);
      console.log(`   Password: ${DRIVER_USER_CONFIG.password}`);
      console.log('⚠️  Remember to change the password after first login!');

    } catch (error) {
      console.error('❌ Driver user seeding failed:', error);
      throw error;
    }
  }

  private async checkExistingUser(): Promise<import('firebase-admin/auth').UserRecord | null> {
    try {
      // Try to get user by email
      const userRecord = await this.auth.getUserByEmail(DRIVER_USER_CONFIG.email);
      return userRecord;
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'code' in error && error.code === 'auth/user-not-found') {
        return null;
      }
      throw error;
    }
  }

  private async createNewDriverUser(): Promise<void> {
    try {
      // Create user in Firebase Auth
      console.log('🔐 Creating Firebase Auth user...');
      const userRecord = await this.auth.createUser({
        email: DRIVER_USER_CONFIG.email,
        password: DRIVER_USER_CONFIG.password,
        displayName: `${DRIVER_USER_CONFIG.firstName} ${DRIVER_USER_CONFIG.lastName}`,
        phoneNumber: DRIVER_USER_CONFIG.phone,
        emailVerified: true,
        disabled: false,
      });

      console.log(`✅ Firebase Auth user created with UID: ${userRecord.uid}`);

      // Create user document in Firestore
      console.log('📄 Creating Firestore user document...');
      await this.createUserDocument(userRecord.uid);

      // Set custom claims for driver role
      console.log('🔑 Setting driver custom claims...');
      await this.auth.setCustomUserClaims(userRecord.uid, {
        roles: DRIVER_USER_CONFIG.roles,
        isDriver: true,
        permissions: ['accept_orders', 'view_profile', 'edit_profile', 'view_earnings', 'update_location']
      });

      console.log('✅ Driver user creation completed successfully');

    } catch (error) {
      console.error('❌ Failed to create driver user:', error);
      throw error;
    }
  }

  private async updateExistingUser(uid: string): Promise<void> {
    try {
      // Update user profile in Firebase Auth
      console.log('🔄 Updating existing user profile...');
      await this.auth.updateUser(uid, {
        displayName: `${DRIVER_USER_CONFIG.firstName} ${DRIVER_USER_CONFIG.lastName}`,
        phoneNumber: DRIVER_USER_CONFIG.phone,
        emailVerified: true,
      });

      // Update or create user document in Firestore
      console.log('📄 Updating Firestore user document...');
      await this.createUserDocument(uid);

      // Update custom claims
      console.log('🔑 Updating driver custom claims...');
      await this.auth.setCustomUserClaims(uid, {
        roles: DRIVER_USER_CONFIG.roles,
        isDriver: true,
        permissions: ['accept_orders', 'view_profile', 'edit_profile', 'view_earnings', 'update_location']
      });

      console.log('✅ Existing user updated successfully');

    } catch (error) {
      console.error('❌ Failed to update existing user:', error);
      throw error;
    }
  }

  private async createUserDocument(uid: string): Promise<void> {
    try {
      const now = Timestamp.now();
      const userData: DriverUserData = {
        profile: {
          ...DRIVER_USER_CONFIG.profile,
          createdAt: now,
          updatedAt: now,
        },
        roles: DRIVER_USER_CONFIG.roles,
        authentication: {
          phoneVerified: true,
          emailVerified: true,
          providers: ['email'],
          lastLogin: now,
          activeUntil: Timestamp.fromDate(new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)), // 1 year
        },
        settings: {
          notifications: {
            orderUpdates: true,
            promotions: true,
            system: true,
          },
          privacy: {
            shareLocation: true,
            showProfile: true,
          },
          theme: 'light',
        },
        stats: {
          totalOrders: 0,
          totalSpent: 0,
          averageRating: 5,
          joinedAt: now,
        },
        status: 'active',
        driver: {
          driverId: `DRV-${uid.slice(-6).toUpperCase()}`,
          licenseNumber: 'JO-123456789',
          licenseExpiry: '2029-12-31',
          vehicleInfo: {
            make: 'Toyota',
            model: 'Camry',
            year: 2020,
            color: 'White',
            plateNumber: 'JO-1234-A',
            vehicleType: 'comfort',
            capacity: 4,
            features: ['AC', 'GPS', 'Child Seat Available'],
          },
          documents: {
            license: 'https://example.com/license.pdf',
            insurance: 'https://example.com/insurance.pdf',
            registration: 'https://example.com/registration.pdf',
            inspection: 'https://example.com/inspection.pdf',
          },
          verification: {
            identityVerified: true,
            licenseVerified: true,
            vehicleVerified: true,
            backgroundCheck: true,
            drugTest: true,
            verifiedAt: now,
          },
          location: {
            current: {
              lat: 31.9539,
              lng: 35.9106,
              updatedAt: now,
            },
            home: {
              lat: 31.9454,
              lng: 35.9284,
              address: 'Al-Rainbow Street, Jabal Amman, Amman',
            },
          },
          availability: {
            isOnline: false,
            workingHours: {
              start: '08:00',
              end: '20:00',
              days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
            },
            maxDistance: 50,
            preferredAreas: ['Amman', 'Zarqa', 'Irbid'],
          },
          earnings: {
            totalEarnings: 0,
            thisWeek: 0,
            thisMonth: 0,
            averagePerRide: 0,
            paymentMethod: 'bank',
            bankInfo: {
              bankName: 'Arab Bank',
              accountNumber: '**********',
              accountHolder: 'Ahmed Driver',
            },
          },
          performance: {
            rating: 5.0,
            totalRides: 0,
            completedRides: 0,
            cancelledRides: 0,
            customerComplaints: 0,
            lastRatingUpdate: now,
          },
          preferences: {
            acceptPooling: true,
            acceptLongDistance: true,
            acceptNightRides: false,
            preferredPaymentMethod: 'cash',
            maxWaitTime: 10,
          },
        },
      };

      // Set document with merge to avoid overwriting existing data
      await this.db.collection('users').doc(uid).set(userData, { merge: true });
      console.log('✅ Firestore user document created/updated successfully');

    } catch (error) {
      console.error('❌ Failed to create/update Firestore document:', error);
      throw error;
    }
  }

  async verifyDriverUser(): Promise<void> {
    try {
      console.log('🔍 Verifying driver user setup...');
      
      const userRecord = await this.auth.getUserByEmail(DRIVER_USER_CONFIG.email);
      console.log(`✅ Firebase Auth user verified: ${userRecord.uid}`);

      const userDoc = await this.db.collection('users').doc(userRecord.uid).get();
      if (userDoc.exists) {
        console.log('✅ Firestore user document verified');
        const userData = userDoc.data();
        console.log(`   Roles: ${userData?.roles?.join(', ')}`);
        console.log(`   Status: ${userData?.status}`);
        console.log(`   Driver ID: ${userData?.driver?.driverId}`);
        console.log(`   Vehicle: ${userData?.driver?.vehicleInfo?.make} ${userData?.driver?.vehicleInfo?.model}`);
        console.log(`   License: ${userData?.driver?.licenseNumber}`);
      } else {
        console.log('❌ Firestore user document not found');
      }

      const customClaims = userRecord.customClaims;
      if (customClaims?.isDriver) {
        console.log('✅ Driver custom claims verified');
        console.log(`   Permissions: ${customClaims.permissions?.join(', ')}`);
      } else {
        console.log('❌ Driver custom claims not found');
      }

    } catch (error) {
      console.error('❌ Driver user verification failed:', error);
      throw error;
    }
  }
}

// Main execution function
async function main(): Promise<void> {
  try {
    console.log('🚕 YellowTaxi Driver User Seeding Script');
    console.log('======================================\n');

    // Validate environment variables
    const requiredEnvVars = [
      'FIREBASE_SERVICE_ACCOUNT_PATH',
      'NEXT_PUBLIC_FIREBASE_PROJECT_ID'
    ];

    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    if (missingEnvVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
    }

    const seeder = new DriverUserSeeder();
    
    // Seed the driver user
    await seeder.seedDriverUser();
    
    console.log('\n🔍 Verifying setup...');
    await seeder.verifyDriverUser();
    
    console.log('\n🎉 Driver user seeding completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Test login with the driver account');
    console.log('   2. Change the default password');
    console.log('   3. Verify driver documents and vehicle information');
    console.log('   4. Test driver dashboard access');
    console.log('   5. Configure working hours and availability');

  } catch (error) {
    console.error('\n❌ Seeding failed:', error);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { DriverUserSeeder, main };
