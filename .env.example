# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id_here
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id_here
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id_here
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id_here

# Firebase Admin SDK (Required for seeding script)
# Path to your Firebase service account key JSON file
# Download this from Firebase Console > Project Settings > Service Accounts > Generate New Private Key
FIREBASE_SERVICE_ACCOUNT_PATH=./firebase-service-account-key.json

# Optional: Custom admin user configuration
# ADMIN_EMAIL=<EMAIL>
# ADMIN_PASSWORD=YellowTaxi2024!
# ADMIN_PHONE=+************

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development