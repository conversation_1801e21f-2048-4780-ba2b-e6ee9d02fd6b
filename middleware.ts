import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/orders',
  '/drivers',
  '/payments',
  '/settings',
  '/users'
];

// Define public routes that are always accessible
const publicRoutes = [
  '/',
  '/login',
  '/register',
  '/verify-otp',
  '/terms',
  '/privacy'
];

// Define API routes that should be handled differently
const apiRoutes = [
  '/api'
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Handle API routes
  if (apiRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.next();
  }

  // Check if the current path is explicitly public
  const isPublicRoute = publicRoutes.some(route => 
    pathname === route || pathname.startsWith(`${route}/`)
  );
  
  // Allow access to public routes
  if (isPublicRoute) {
    return NextResponse.next();
  }

  // Check if the current path is protected
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  );
  
  // For protected routes, we'll let the client-side handle authentication
  // since middleware runs on the edge and can't access Firebase client SDKs
  if (isProtectedRoute) {
    // The PageGuard components will handle the actual auth checking
    return NextResponse.next();
  }

  // Handle static files and other assets
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/public/') ||
    pathname.includes('.') // Files with extensions
  ) {
    return NextResponse.next();
  }

  // For all other routes, allow access
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
