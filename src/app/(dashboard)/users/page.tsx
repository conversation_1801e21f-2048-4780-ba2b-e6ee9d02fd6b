'use client'

import { useState, useCallback, memo, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useUsers } from '@/hooks/useUsers';
import { Users, Plus, AlertCircle, X } from 'lucide-react';
import { UserStats } from '@/components/dashboard/UserStats';
import { UserFilters } from '@/components/dashboard/UserFilters';
import { UserTable } from '@/components/dashboard/UserTable';
import { CreateUserModal } from '@/components/dashboard/CreateUserModal';
import { EditUserModal } from '@/components/dashboard/EditUserModal';
import { DeleteUserModal } from '@/components/dashboard/DeleteUserModal';
import { ViewUserModal } from '@/components/dashboard/ViewUserModal';
import { UserDocument, UserRole } from '@/types/database';
import { CreateUserData, UpdateUserData } from '@/services/UserService';
import { useToast } from '@/hooks/use-toast';

// Memoize the main UsersPage component
const UsersPage = memo(() => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserDocument | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const {
    users,
    loading,
    error,
    stats,
    filters,
    hasMore,
    setFilters,
    searchUsers,
    createUser,
    updateUser,
    updateUserRoles,
    updateUserStatus,
    deleteUser,
    verifyUserPhone,
    verifyUserEmail,
    bulkUpdateUsers,
    loadMoreUsers,
    refreshUsers,
    exportUsers,
    clearError
  } = useUsers();

  // Check if user has permission to manage users
  const canManageUsers = userProfile?.roles.includes('admin') || 
                        userProfile?.roles.includes('office_manager');

  // Memoize handlers to prevent unnecessary re-renders
  const handleCreateUser = useCallback(async (userData: CreateUserData) => {
    try {
      const userId = await createUser(userData);
      toast({
        title: "Success",
        description: `User created successfully with ID: ${userId}`,
      });
      return userId;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create user",
        variant: "destructive",
      });
      throw error;
    }
  }, [createUser, toast]);

  const handleEditUser = useCallback((user: UserDocument) => {
    setSelectedUser(user);
    setShowEditModal(true);
  }, []);

  const handleViewUser = useCallback((user: UserDocument) => {
    setSelectedUser(user);
    setShowViewModal(true);
  }, []);

  const handleUpdateStatus = useCallback(async (userId: string, status: UserDocument['status']) => {
    try {
      await updateUserStatus(userId, status);
      toast({
        title: "Success",
        description: `User status updated to ${status}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update user status",
        variant: "destructive",
      });
    }
  }, [updateUserStatus, toast]);

  const handleUpdateRoles = useCallback(async (userId: string, roles: UserRole[]) => {
    try {
      await updateUserRoles(userId, roles);
      toast({
        title: "Success",
        description: "User roles updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update user roles",
        variant: "destructive",
      });
    }
  }, [updateUserRoles, toast]);

  const handleVerifyPhone = useCallback(async (userId: string) => {
    try {
      await verifyUserPhone(userId);
      toast({
        title: "Success",
        description: "Phone number verified successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to verify phone number",
        variant: "destructive",
      });
    }
  }, [verifyUserPhone, toast]);

  const handleVerifyEmail = useCallback(async (userId: string) => {
    try {
      await verifyUserEmail(userId);
      toast({
        title: "Success",
        description: "Email verified successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to verify email",
        variant: "destructive",
      });
    }
  }, [verifyUserEmail, toast]);

  const handleDeleteUser = useCallback((user: UserDocument) => {
    setSelectedUser(user);
    setShowDeleteModal(true);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (!selectedUser) return;
    
    try {
      setDeleteLoading(true);
      await deleteUser(selectedUser.id);
      toast({
        title: "Success",
        description: "User permanently deleted from the system",
      });
      setShowDeleteModal(false);
      setSelectedUser(null);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete user",
        variant: "destructive",
      });
    } finally {
      setDeleteLoading(false);
    }
  }, [selectedUser, deleteUser, toast]);

  const handleCloseDeleteModal = useCallback(() => {
    setShowDeleteModal(false);
    setSelectedUser(null);
    setDeleteLoading(false);
  }, []);

  const handleUpdateUser = useCallback(async (userId: string, userData: UpdateUserData) => {
    try {
      await updateUser(userId, userData);
      toast({
        title: "Success",
        description: "User updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update user",
        variant: "destructive",
      });
      throw error;
    }
  }, [updateUser, toast]);

  const handleCloseEditModal = useCallback(() => {
    setShowEditModal(false);
    setSelectedUser(null);
  }, []);

  const handleCloseViewModal = useCallback(() => {
    setShowViewModal(false);
    setSelectedUser(null);
  }, []);


  const handleExportAll = useCallback(async () => {
    try {
      const usersData = await exportUsers();
      // Create and download CSV
      const csvContent = convertToCSV(usersData);
      downloadCSV(csvContent, 'users-export.csv');
      toast({
        title: "Success",
        description: "Users exported successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export users",
        variant: "destructive",
      });
    }
  }, [exportUsers, toast]);

  const handleRefresh = useCallback(async () => {
    try {
      await refreshUsers();
      toast({
        title: "Success",
        description: "Users refreshed successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to refresh users",
        variant: "destructive",
      });
    }
  }, [refreshUsers, toast]);

  const handleSearch = useCallback(async (searchTerm: string) => {
    try {
      await searchUsers(searchTerm);
    } catch (error) {
      toast({
        title: "Error",
        description: "Search failed",
        variant: "destructive",
      });
    }
  }, [searchUsers, toast]);

  const handleFiltersChange = useCallback((newFilters: typeof filters) => {
    setFilters(newFilters);
  }, [setFilters]);

  const handleShowCreateModal = useCallback(() => {
    setShowCreateModal(true);
  }, []);

  const handleCloseCreateModal = useCallback(() => {
    setShowCreateModal(false);
  }, []);

  // Helper function to convert users to CSV
  const convertToCSV = useCallback((users: UserDocument[]) => {
    const headers = [
      'ID',
      'First Name',
      'Last Name',
      'Email',
      'Phone',
      'Roles',
      'Status',
      'Phone Verified',
      'Email Verified',
      'Created At'
    ];

    const rows = users.map(user => [
      user.id,
      user.profile.firstName,
      user.profile.lastName,
      user.profile.email || '',
      user.profile.phone,
      user.roles.join(', '),
      user.status,
      user.authentication.phoneVerified ? 'Yes' : 'No',
      user.authentication.emailVerified ? 'Yes' : 'No',
      user.profile.createdAt?.toDate?.()?.toISOString() || 'N/A'
    ]);

    return [headers, ...rows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');
  }, []);

  // Helper function to download CSV
  const downloadCSV = useCallback((content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, []);

  // Early return for loading state to prevent flickering
  if (!userProfile) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading users...</p>
        </div>
      </div>
    );
  }

  if (!canManageUsers) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">You don&apos;t have permission to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Users Management</h1>
          <p className="text-gray-600 mt-2">Manage all Yellow Taxi users and their accounts</p>
        </div>
        <button
          onClick={handleShowCreateModal}
          className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-black px-6 py-3 rounded-xl font-semibold hover:from-yellow-600 hover:to-yellow-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
        >
          <Plus className="w-5 h-5 inline mr-2" />
          Add New User
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="glass-effect rounded-2xl p-4 border border-red-200 bg-red-50">
          <div className="flex items-center space-x-3">
            <AlertCircle className="w-5 h-5 text-red-500" />
            <div className="flex-1">
              <p className="text-red-800 font-medium">Error</p>
              <p className="text-red-600 text-sm">{error}</p>
            </div>
            <button
              onClick={clearError}
              className="text-red-500 hover:text-red-700"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* User Statistics */}
      <UserStats stats={stats} loading={loading} />

      {/* Search and Filters */}
      <UserFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onSearch={handleSearch}
        onExport={handleExportAll}
        onRefresh={handleRefresh}
        loading={loading}
      />

      {/* Users Table */}
      <UserTable
        users={users}
        loading={loading}
        onEditUser={handleEditUser}
        onViewUser={handleViewUser}
        onUpdateStatus={handleUpdateStatus}
        onUpdateRoles={handleUpdateRoles}
        onVerifyPhone={handleVerifyPhone}
        onVerifyEmail={handleVerifyEmail}
        onDeleteUser={handleDeleteUser}
      />

      {/* Load More Button */}
      {hasMore && (
        <div className="text-center">
          <button
            onClick={loadMoreUsers}
            disabled={loading}
            className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Loading...' : 'Load More Users'}
          </button>
        </div>
      )}

      {/* Create User Modal */}
      <CreateUserModal
        isOpen={showCreateModal}
        onClose={handleCloseCreateModal}
        onCreateUser={handleCreateUser}
      />

      {/* Edit User Modal */}
      <EditUserModal
        isOpen={showEditModal}
        user={selectedUser}
        onClose={handleCloseEditModal}
        onUpdateUser={handleUpdateUser}
      />

      {/* Delete User Modal */}
      <DeleteUserModal
        isOpen={showDeleteModal}
        user={selectedUser}
        onClose={handleCloseDeleteModal}
        onConfirm={handleConfirmDelete}
        loading={deleteLoading}
      />

      {/* View User Modal */}
      <ViewUserModal
        isOpen={showViewModal}
        user={selectedUser}
        onClose={handleCloseViewModal}
      />
    </div>
  );
});

UsersPage.displayName = 'UsersPage';

export default UsersPage;
