'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  MapPin, 
  Shield, 
  Star,
  Edit3,
  Save,
  X,
  Camera
} from 'lucide-react'
import { doc, updateDoc } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { UserDocument } from '@/types/database'

export default function ProfilePage() {
  const { userProfile, user } = useAuth()
  const { toast } = useToast()
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    gender: 'male' as const,
    birthDate: '',
    language: 'en' as const,
    bio: ''
  })

  useEffect(() => {
    if (userProfile) {
      setFormData({
        firstName: userProfile.profile.firstName || '',
        lastName: userProfile.profile.lastName || '',
        email: userProfile.profile.email || '',
        phone: userProfile.profile.phone || '',
        gender: userProfile.profile.gender || 'male',
        birthDate: userProfile.profile.birthDate || '',
        language: userProfile.profile.language || 'en',
        bio: ''
      })
    }
  }, [userProfile])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    if (!userProfile || !user) return

    setIsLoading(true)
    try {
      const userRef = doc(db, 'users', user.uid)
      await updateDoc(userRef, {
        'profile.firstName': formData.firstName,
        'profile.lastName': formData.lastName,
        'profile.email': formData.email,
        'profile.gender': formData.gender,
        'profile.birthDate': formData.birthDate,
        'profile.language': formData.language,
        'profile.updatedAt': new Date()
      })

      toast({
        title: "Profile Updated",
        description: "Your profile has been successfully updated.",
      })
      
      setIsEditing(false)
    } catch (error) {
      console.error('Error updating profile:', error)
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    if (userProfile) {
      setFormData({
        firstName: userProfile.profile.firstName || '',
        lastName: userProfile.profile.lastName || '',
        email: userProfile.profile.email || '',
        phone: userProfile.profile.phone || '',
        gender: userProfile.profile.gender || 'male',
        birthDate: userProfile.profile.birthDate || '',
        language: userProfile.profile.language || 'en',
        bio: ''
      })
    }
    setIsEditing(false)
  }

  if (!userProfile) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading profile...</p>
        </div>
      </div>
    )
  }

  const getInitials = () => {
    const firstName = userProfile.profile.firstName || ''
    const lastName = userProfile.profile.lastName || ''
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || 'U'
  }

  const getPrimaryRole = () => {
    if (userProfile.roles.includes('admin')) return 'Administrator'
    if (userProfile.roles.includes('driver')) return 'Driver'
    if (userProfile.roles.includes('office_manager')) return 'Office Manager'
    if (userProfile.roles.includes('support')) return 'Support'
    return 'Customer'
  }

  const getRoleColor = () => {
    if (userProfile.roles.includes('admin')) return 'bg-red-100 text-red-800'
    if (userProfile.roles.includes('driver')) return 'bg-blue-100 text-blue-800'
    if (userProfile.roles.includes('office_manager')) return 'bg-purple-100 text-purple-800'
    if (userProfile.roles.includes('support')) return 'bg-green-100 text-green-800'
    return 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="space-y-6">
      {/* Profile Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Avatar className="h-20 w-20">
                <AvatarImage src={userProfile.profile.avatar} alt={userProfile.profile.firstName} />
                <AvatarFallback className="text-2xl">
                  {getInitials()}
                </AvatarFallback>
              </Avatar>
              {isEditing && (
                <Button
                  size="sm"
                  variant="outline"
                  className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0"
                >
                  <Camera className="h-4 w-4" />
                </Button>
              )}
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {userProfile.profile.firstName} {userProfile.profile.lastName}
              </h1>
              <p className="text-gray-600 mt-1">
                {getPrimaryRole()} • Member since {new Date(userProfile.stats.joinedAt.seconds * 1000).toLocaleDateString()}
              </p>
              <div className="flex items-center space-x-2 mt-2">
                <Badge className={getRoleColor()}>
                  {getPrimaryRole()}
                </Badge>
                <Badge variant="outline">
                  <Star className="h-3 w-3 mr-1" />
                  {userProfile.stats.averageRating.toFixed(1)} Rating
                </Badge>
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            {!isEditing ? (
              <Button onClick={() => setIsEditing(true)}>
                <Edit3 className="mr-2 h-4 w-4" />
                Edit Profile
              </Button>
            ) : (
              <>
                <Button onClick={handleSave} disabled={isLoading}>
                  <Save className="mr-2 h-4 w-4" />
                  {isLoading ? 'Saving...' : 'Save Changes'}
                </Button>
                <Button variant="outline" onClick={handleCancel}>
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Personal Information */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>
                Your basic profile details and contact information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  {isEditing ? (
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      placeholder="Enter your first name"
                    />
                  ) : (
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-md">
                      <User className="h-4 w-4 text-gray-400" />
                      <span>{userProfile.profile.firstName || 'Not provided'}</span>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  {isEditing ? (
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      placeholder="Enter your last name"
                    />
                  ) : (
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-md">
                      <User className="h-4 w-4 text-gray-400" />
                      <span>{userProfile.profile.lastName || 'Not provided'}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  {isEditing ? (
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="Enter your email address"
                    />
                  ) : (
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-md">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <span>{userProfile.profile.email || 'Not provided'}</span>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-md">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span>{userProfile.profile.phone}</span>
                    <Badge variant="outline" className="ml-auto">
                      {userProfile.authentication.phoneVerified ? 'Verified' : 'Unverified'}
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="gender">Gender</Label>
                  {isEditing ? (
                    <Select value={formData.gender} onValueChange={(value: 'male' | 'female') => handleInputChange('gender', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-md">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="capitalize">{userProfile.profile.gender || 'Not specified'}</span>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="birthDate">Birth Date</Label>
                  {isEditing ? (
                    <Input
                      id="birthDate"
                      type="date"
                      value={formData.birthDate}
                      onChange={(e) => handleInputChange('birthDate', e.target.value)}
                    />
                  ) : (
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-md">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span>{userProfile.profile.birthDate || 'Not provided'}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="language">Preferred Language</Label>
                {isEditing ? (
                  <Select value={formData.language} onValueChange={(value: 'en' | 'ar') => handleInputChange('language', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="ar">العربية</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-md">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span>{userProfile.profile.language === 'ar' ? 'العربية' : 'English'}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Account Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Account Statistics</CardTitle>
              <CardDescription>
                Your activity and usage statistics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{userProfile.stats.totalOrders}</div>
                  <div className="text-sm text-blue-600">
                    {userProfile.roles.includes('driver') ? 'Completed Trips' : 'Total Rides'}
                  </div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">JOD {userProfile.stats.totalSpent.toFixed(2)}</div>
                  <div className="text-sm text-green-600">
                    {userProfile.roles.includes('driver') ? 'Total Earnings' : 'Total Spent'}
                  </div>
                </div>
                <div className="text-center p-4 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">{userProfile.stats.averageRating.toFixed(1)}</div>
                  <div className="text-sm text-yellow-600">Average Rating</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Account Status */}
          <Card>
            <CardHeader>
              <CardTitle>Account Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Status</span>
                <Badge variant={userProfile.status === 'active' ? 'default' : 'destructive'}>
                  {userProfile.status}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Phone Verified</span>
                <Badge variant={userProfile.authentication.phoneVerified ? 'default' : 'secondary'}>
                  {userProfile.authentication.phoneVerified ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Email Verified</span>
                <Badge variant={userProfile.authentication.emailVerified ? 'default' : 'secondary'}>
                  {userProfile.authentication.emailVerified ? 'Yes' : 'No'}
                </Badge>
              </div>
              <Separator />
              <div className="text-xs text-gray-500">
                Last login: {new Date(userProfile.authentication.lastLogin.seconds * 1000).toLocaleString()}
              </div>
            </CardContent>
          </Card>

          {/* Authentication Providers */}
          <Card>
            <CardHeader>
              <CardTitle>Connected Accounts</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {userProfile.authentication.providers.map((provider) => (
                <div key={provider} className="flex items-center space-x-2">
                  <Shield className="h-4 w-4 text-green-500" />
                  <span className="text-sm capitalize">{provider}</span>
                  <Badge variant="outline" className="ml-auto">Connected</Badge>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <Shield className="mr-2 h-4 w-4" />
                Change Password
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Mail className="mr-2 h-4 w-4" />
                Verify Email
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <User className="mr-2 h-4 w-4" />
                Privacy Settings
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
