'use client'

import { useAuth } from '@/hooks/useAuth'
import { Settings, User, Shield, Bell, Globe, CreditCard, Database, Key } from 'lucide-react'

export default function SettingsPage() {
  const { userProfile } = useAuth()

  if (!userProfile) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading settings...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600 mt-2">Manage your Yellow Taxi account preferences and system configuration</p>
        </div>
      </div>

      {/* Settings Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Profile Settings */}
        <div className="glass-effect rounded-2xl p-6 border border-white/20 hover:shadow-lg transition-all duration-300 cursor-pointer group">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-12 h-12 rounded-xl bg-yellow-100 flex items-center justify-center group-hover:scale-110 transition-all duration-300">
              <User className="w-6 h-6 text-yellow-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Profile Settings</h3>
              <p className="text-sm text-gray-500">Personal information and preferences</p>
            </div>
          </div>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Update personal details</p>
            <p>• Change profile picture</p>
            <p>• Manage contact information</p>
          </div>
        </div>

        {/* Security Settings */}
        <div className="glass-effect rounded-2xl p-6 border border-white/20 hover:shadow-lg transition-all duration-300 cursor-pointer group">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-12 h-12 rounded-xl bg-red-100 flex items-center justify-center group-hover:scale-110 transition-all duration-300">
              <Shield className="w-6 h-6 text-red-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Security</h3>
              <p className="text-sm text-gray-500">Password and authentication</p>
            </div>
          </div>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Change password</p>
            <p>• Two-factor authentication</p>
            <p>• Login history</p>
          </div>
        </div>

        {/* Notifications */}
        <div className="glass-effect rounded-2xl p-6 border border-white/20 hover:shadow-lg transition-all duration-300 cursor-pointer group">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-12 h-12 rounded-xl bg-blue-100 flex items-center justify-center group-hover:scale-110 transition-all duration-300">
              <Bell className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
              <p className="text-sm text-gray-500">Alert preferences</p>
            </div>
          </div>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Email notifications</p>
            <p>• Push notifications</p>
            <p>• SMS alerts</p>
          </div>
        </div>

        {/* Language & Region */}
        <div className="glass-effect rounded-2xl p-6 border border-white/20 hover:shadow-lg transition-all duration-300 cursor-pointer group">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-12 h-12 rounded-xl bg-green-100 flex items-center justify-center group-hover:scale-110 transition-all duration-300">
              <Globe className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Language & Region</h3>
              <p className="text-sm text-gray-500">Localization settings</p>
            </div>
          </div>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Language selection</p>
            <p>• Time zone settings</p>
            <p>• Currency preferences</p>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="glass-effect rounded-2xl p-6 border border-white/20 hover:shadow-lg transition-all duration-300 cursor-pointer group">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-12 h-12 rounded-xl bg-purple-100 flex items-center justify-center group-hover:scale-110 transition-all duration-300">
              <CreditCard className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Payment Methods</h3>
              <p className="text-sm text-gray-500">Billing and payment</p>
            </div>
          </div>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Saved payment methods</p>
            <p>• Billing address</p>
            <p>• Payment history</p>
          </div>
        </div>

        {/* System Settings */}
        <div className="glass-effect rounded-2xl p-6 border border-white/20 hover:shadow-lg transition-all duration-300 cursor-pointer group">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-12 h-12 rounded-xl bg-gray-100 flex items-center justify-center group-hover:scale-110 transition-all duration-300">
              <Database className="w-6 h-6 text-gray-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">System</h3>
              <p className="text-sm text-gray-500">Technical configuration</p>
            </div>
          </div>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• API keys</p>
            <p>• Webhook settings</p>
            <p>• Data export</p>
          </div>
        </div>
      </div>

      {/* Current Settings Preview */}
      <div className="glass-effect rounded-2xl p-6 border border-white/20">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Settings</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Language</span>
              <span className="text-sm font-medium text-gray-900">English</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Time Zone</span>
              <span className="text-sm font-medium text-gray-900">Asia/Amman (UTC+3)</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Currency</span>
              <span className="text-sm font-medium text-gray-900">Jordanian Dinar (JD)</span>
            </div>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Email Notifications</span>
              <span className="text-sm font-medium text-green-600">Enabled</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Push Notifications</span>
              <span className="text-sm font-medium text-green-600">Enabled</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Two-Factor Auth</span>
              <span className="text-sm font-medium text-red-600">Disabled</span>
            </div>
          </div>
        </div>
      </div>

      {/* API Keys Section */}
      <div className="glass-effect rounded-2xl p-6 border border-white/20">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">API Keys</h3>
          <button className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-black px-4 py-2 rounded-lg font-medium hover:from-yellow-600 hover:to-yellow-700 transition-all duration-300">
            <Key className="w-4 h-4 inline mr-2" />
            Generate New Key
          </button>
        </div>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <p className="text-sm font-medium text-gray-900">Production API Key</p>
              <p className="text-xs text-gray-500">Created: Oct 15, 2024</p>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-xs font-mono text-gray-600">••••••••••••••••</span>
              <button className="text-yellow-600 hover:text-yellow-700 text-sm font-medium">Show</button>
            </div>
          </div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <p className="text-sm font-medium text-gray-900">Test API Key</p>
              <p className="text-xs text-gray-500">Created: Oct 10, 2024</p>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-xs font-mono text-gray-600">••••••••••••••••</span>
              <button className="text-yellow-600 hover:text-yellow-700 text-sm font-medium">Show</button>
            </div>
          </div>
        </div>
      </div>

      {/* Placeholder Content */}
      <div className="glass-effect rounded-2xl p-8 border border-white/20 text-center">
        <Settings className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Settings Management</h3>
        <p className="text-gray-600 mb-4">
          This page will contain comprehensive settings management including:
        </p>
        <ul className="text-sm text-gray-500 space-y-1">
          <li>• User profile and preferences management</li>
          <li>• Security and authentication settings</li>
          <li>• Notification preferences and alerts</li>
          <li>• System configuration and API management</li>
          <li>• Localization and regional settings</li>
        </ul>
      </div>
    </div>
  )
}
