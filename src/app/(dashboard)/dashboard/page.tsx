'use client'

import { useAuth } from '@/hooks/useAuth'
import { 
  <PERSON><PERSON><PERSON>Up, 
  FileCheck, 
  XCircle, 
  Clock, 
  CheckCircle,
  Plus,
  Download,
  Users,
  ArrowRight,
  ArrowDownRight,
  ArrowUpRight,
  MoreHorizontal
} from 'lucide-react'
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Filler } from 'chart.js'
import { Line } from 'react-chartjs-2'

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Filler)

export default function DashboardPage() {
  const { userProfile } = useAuth()

  if (!userProfile) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }



  // Chart data
  const chartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [{
      label: 'Revenue',
      data: [65000, 78000, 85000, 92000, 88000, 94000],
      borderColor: '#FFDC00',
      backgroundColor: 'rgba(255, 220, 0, 0.1)',
      borderWidth: 3,
      fill: true,
      tension: 0.4
    }]
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        }
      },
      x: {
        grid: {
          display: false
        }
      }
    }
  }

  return (
    <div className="space-y-12">
      {/* Business Overview Cards */}
      <section className="mb-12">
        <div className="flex items-center justify-between mb-8">
          <h2 className="opacity-0 animate-fade-in-up delay-200 text-2xl tracking-tight bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent font-semibold">
            Business Overview
          </h2>
          <button className="opacity-0 animate-fade-in-up delay-200 text-gray-500 flex items-center space-x-2 glass-effect px-4 py-2 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 group">
            <span className="text-sm font-medium">This month</span>
            <svg className="w-4 h-4 group-hover:translate-y-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          {/* Total Revenue */}
          <div className="opacity-0 animate-scale-in delay-300 relative glass-effect rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 group transform hover:-translate-y-2 border border-white/20">
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
            <div className="absolute -top-3 -right-3 bg-gradient-to-r from-green-500 to-green-600 text-xs font-bold rounded-full px-3 py-1.5 text-white shadow-lg animate-pulse">+28%</div>
            <div className="w-14 h-14 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-all duration-300 bg-gradient-to-br from-yellow-500 to-yellow-600 shadow-lg">
              <TrendingUp className="w-6 h-6 text-black" strokeWidth={1.5} />
            </div>
            <p className="text-sm text-gray-500 mb-3 font-medium">Total Revenue</p>
            <p className="text-3xl mb-2 text-gray-900 number-counter font-semibold">JD 186,404</p>
            <p className="text-xs text-gray-400 font-medium">+JD 23,105 from last month</p>
          </div>

          {/* Active Rides */}
          <div className="opacity-0 animate-scale-in delay-400 relative glass-effect rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 group transform hover:-translate-y-2 border border-white/20">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
            <div className="absolute -top-3 -right-3 bg-gradient-to-r from-blue-500 to-blue-600 text-xs font-bold rounded-full px-3 py-1.5 text-white shadow-lg animate-pulse">+15%</div>
            <div className="w-14 h-14 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-all duration-300 bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg">
              <FileCheck className="w-6 h-6 text-white" strokeWidth={1.5} />
            </div>
            <p className="text-sm text-gray-500 mb-3 font-medium">Active Rides</p>
            <p className="text-3xl mb-2 text-gray-900 number-counter font-semibold">127</p>
            <p className="text-xs text-gray-400 font-medium">18 completed today</p>
          </div>

          {/* Cancelled Rides */}
          <div className="opacity-0 animate-scale-in delay-500 relative glass-effect rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 group transform hover:-translate-y-2 border border-white/20">
            <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-pink-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
            <div className="absolute -top-3 -right-3 bg-gradient-to-r from-red-500 to-red-600 text-xs font-bold rounded-full px-3 py-1.5 text-white shadow-lg animate-pulse">-8%</div>
            <div className="w-14 h-14 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-all duration-300 bg-gradient-to-br from-red-500 to-red-600 shadow-lg">
              <XCircle className="w-6 h-6 text-white" strokeWidth={1.5} />
            </div>
            <p className="text-sm text-gray-500 mb-3 font-medium">Cancelled Rides</p>
            <p className="text-3xl mb-2 text-gray-900 number-counter font-semibold">23</p>
            <p className="text-xs text-gray-400 font-medium">3 cancelled this week</p>
          </div>

          {/* Pending Drivers */}
          <div className="opacity-0 animate-scale-in delay-600 relative glass-effect rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 group transform hover:-translate-y-2 border border-white/20">
            <div className="absolute inset-0 bg-gradient-to-br from-amber-500/10 to-orange-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
            <div className="absolute -top-3 -right-3 bg-gradient-to-r from-amber-500 to-orange-500 text-xs font-bold rounded-full px-3 py-1.5 text-white shadow-lg animate-pulse">+12%</div>
            <div className="w-14 h-14 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-all duration-300 bg-gradient-to-br from-amber-500 to-amber-600 shadow-lg">
              <Clock className="w-6 h-6 text-white" strokeWidth={1.5} />
            </div>
            <p className="text-sm text-gray-500 mb-3 font-medium">Pending Drivers</p>
            <p className="text-3xl mb-2 text-gray-900 number-counter font-semibold">94</p>
            <p className="text-xs text-gray-400 font-medium">Average 2.3 days review</p>
          </div>

          {/* Completed Rides */}
          <div className="opacity-0 animate-scale-in delay-700 relative glass-effect rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 group transform hover:-translate-y-2 border border-white/20">
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
            <div className="absolute -top-3 -right-3 bg-gradient-to-r from-green-500 to-green-600 text-xs font-bold rounded-full px-3 py-1.5 text-white shadow-lg animate-pulse">+35%</div>
            <div className="w-14 h-14 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-all duration-300 bg-gradient-to-br from-green-500 to-green-600 shadow-lg">
              <CheckCircle className="w-6 h-6 text-white" strokeWidth={1.5} />
            </div>
            <p className="text-sm text-gray-500 mb-3 font-medium">Completed Rides</p>
            <p className="text-3xl mb-2 text-gray-900 number-counter font-semibold">1,456</p>
            <p className="text-xs text-gray-400 font-medium">+412 this month</p>
          </div>
        </div>
      </section>

      {/* Charts Section */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 mb-12">
        {/* Revenue Chart */}
        <div className="opacity-0 animate-fade-in-up delay-800 xl:col-span-2 glass-effect rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/20">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Revenue Analytics</h3>
              <p className="text-sm text-gray-500">Monthly performance overview</p>
            </div>
            <div className="flex items-center space-x-3">
              <button className="text-xs font-medium px-4 py-2 rounded-xl bg-yellow-100 text-yellow-700 hover:bg-yellow-200 transition-all duration-300">6M</button>
              <button className="text-xs font-medium px-4 py-2 rounded-xl text-gray-500 hover:bg-gray-100 transition-all duration-300">1Y</button>
            </div>
          </div>
          <div className="h-80">
            <Line data={chartData} options={chartOptions} />
          </div>
        </div>

        {/* Quick Actions */}
        <div className="opacity-0 animate-fade-in-right delay-900 glass-effect rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/20">
          <h3 className="text-xl font-bold text-gray-900 mb-6">Quick Actions</h3>
          <div className="space-y-4">
            <button className="w-full flex items-center justify-between p-4 rounded-2xl bg-gradient-to-r from-yellow-500 to-yellow-600 text-black hover:from-yellow-600 hover:to-yellow-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-xl bg-black/20 flex items-center justify-center">
                  <Plus className="w-5 h-5" strokeWidth={2} />
                </div>
                <span className="font-medium">New Ride</span>
              </div>
              <ArrowRight className="w-4 h-4" strokeWidth={2} />
            </button>
            
            <button className="w-full flex items-center justify-between p-4 rounded-2xl bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-xl bg-white/20 flex items-center justify-center">
                  <Download className="w-5 h-5" strokeWidth={2} />
                </div>
                <span className="font-medium">Export Report</span>
              </div>
              <ArrowRight className="w-4 h-4" strokeWidth={2} />
            </button>
            
            <button className="w-full flex items-center justify-between p-4 rounded-2xl bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-xl bg-white/20 flex items-center justify-center">
                  <Users className="w-5 h-5" strokeWidth={2} />
                </div>
                <span className="font-medium">Driver Analytics</span>
              </div>
              <ArrowRight className="w-4 h-4" strokeWidth={2} />
            </button>
          </div>
          
          <div className="mt-8 p-6 rounded-2xl bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200">
            <h4 className="font-semibold text-gray-900 mb-3">Recent Activity</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <p className="text-sm text-gray-600">Ride #1847 completed</p>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                <p className="text-sm text-gray-600">New driver approved</p>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 rounded-full bg-amber-500"></div>
                <p className="text-sm text-gray-600">Payment pending review</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Rides */}
      <section className="opacity-0 animate-fade-in-up delay-1000">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl tracking-tight bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent font-semibold">
            Recent Rides
          </h2>
          <button className="text-yellow-600 hover:text-yellow-700 font-medium text-sm flex items-center space-x-2 transition-all duration-300 group">
            <span>View all</span>
            <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" strokeWidth={2} />
          </button>
        </div>
        
        <div className="glass-effect rounded-3xl shadow-xl border border-white/20 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50/50">
                <tr>
                  <th className="px-8 py-6 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Ride</th>
                  <th className="px-6 py-6 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Amount</th>
                  <th className="px-6 py-6 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-6 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Date</th>
                  <th className="px-8 py-6 text-right text-xs font-semibold text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100">
                <tr className="hover:bg-gray-50/50 transition-all duration-300">
                  <td className="px-8 py-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-yellow-500 to-yellow-600 flex items-center justify-center shadow-lg">
                        <ArrowDownRight className="w-5 h-5 text-black" strokeWidth={2} />
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900">Airport Pickup</p>
                        <p className="text-sm text-gray-500">Queen Alia Airport → Amman</p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-6 font-semibold text-gray-900">JD 25.50</td>
                  <td className="px-6 py-6">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Completed</span>
                  </td>
                  <td className="px-6 py-6 text-sm text-gray-500">Oct 15, 2024</td>
                  <td className="px-8 py-6 text-right">
                    <button className="text-gray-400 hover:text-gray-600 transition-colors duration-300">
                      <MoreHorizontal className="w-5 h-5" strokeWidth={2} />
                    </button>
                  </td>
                </tr>
                <tr className="hover:bg-gray-50/50 transition-all duration-300">
                  <td className="px-8 py-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center shadow-lg">
                        <ArrowUpRight className="w-5 h-5 text-white" strokeWidth={2} />
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900">City Center Trip</p>
                        <p className="text-sm text-gray-500">Abdoun → Downtown Amman</p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-6 font-semibold text-green-600">JD 12.75</td>
                  <td className="px-6 py-6">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Completed</span>
                  </td>
                  <td className="px-6 py-6 text-sm text-gray-500">Oct 14, 2024</td>
                  <td className="px-8 py-6 text-right">
                    <button className="text-gray-400 hover:text-gray-600 transition-colors duration-300">
                      <MoreHorizontal className="w-5 h-5" strokeWidth={2} />
                    </button>
                  </td>
                </tr>
                <tr className="hover:bg-gray-50/50 transition-all duration-300">
                  <td className="px-8 py-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-amber-500 to-amber-600 flex items-center justify-center shadow-lg">
                        <Clock className="w-5 h-5 text-white" strokeWidth={2} />
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900">University Trip</p>
                        <p className="text-sm text-gray-500">University of Jordan</p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-6 font-semibold text-gray-900">JD 8.00</td>
                  <td className="px-6 py-6">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">In Progress</span>
                  </td>
                  <td className="px-6 py-6 text-sm text-gray-500">Oct 13, 2024</td>
                  <td className="px-8 py-6 text-right">
                    <button className="text-gray-400 hover:text-gray-600 transition-colors duration-300">
                      <MoreHorizontal className="w-5 h-5" strokeWidth={2} />
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
  )
}