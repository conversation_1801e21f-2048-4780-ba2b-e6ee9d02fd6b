'use client'

import { useAuth } from '@/hooks/useAuth'
import { useOrders } from '@/hooks/useOrders'
import { useState, useEffect } from 'react'
import { MapPin, Clock, User, Phone, Navigation, CheckCircle, XCircle, Car } from 'lucide-react'
import { OrderDocument, OrderStatus } from '@/types/database'
import toast from 'react-hot-toast'

export default function DriverDashboard() {
  const { userProfile } = useAuth()
  const { orders, updateOrderStatus, subscribeToUserOrders } = useOrders()
  const [isOnline, setIsOnline] = useState(false)
  const [activeOrder, setActiveOrder] = useState<OrderDocument | null>(null)
  const [availableOrders, setAvailableOrders] = useState<OrderDocument[]>([])

  // Subscribe to driver orders when component mounts
  useEffect(() => {
    if (userProfile?.id) {
      subscribeToUserOrders(userProfile.id, 'driver')
    }
  }, [userProfile?.id]) // Remove subscribeToUserOrders from dependencies

  // Separate active and available orders
  useEffect(() => {
    const active = orders.find(order => 
      order.driver?.id === userProfile?.id &&
      ['assigned', 'driver_arriving', 'driver_arrived', 'picked_up', 'in_progress'].includes(order.status.current)
    )
    setActiveOrder(active || null)

    // For demo purposes, show some pending orders as available
    // In a real app, this would be based on driver location and availability
    const available = orders.filter(order => 
      order.status.current === 'pending' || order.status.current === 'searching'
    ).slice(0, 3)
    setAvailableOrders(available)
  }, [orders, userProfile?.id])

  const handleAcceptOrder = async (orderId: string) => {
    if (!userProfile) return

    try {
      // In a real app, you'd get actual driver and vehicle data
      const driverData = {
        id: userProfile.id,
        name: `${userProfile.profile.firstName} ${userProfile.profile.lastName}`,
        phone: userProfile.profile.phone,
        avatar: userProfile.profile.avatar || '',
        vehicle: {
          make: 'Toyota',
          model: 'Camry',
          year: 2020,
          color: 'White',
          plateNumber: 'JO-1234-A',
          type: 'standard'
        },
        location: { lat: 31.9539, lng: 35.9106 } // Demo location in Amman
      }

      await updateOrderStatus(orderId, 'assigned', 'Driver accepted the ride', driverData)
      toast.success('Ride accepted! Heading to pickup location.')
    } catch (error) {
      toast.error('Failed to accept ride')
    }
  }

  const handleRejectOrder = async (orderId: string) => {
    try {
      await updateOrderStatus(orderId, 'cancelled', 'Driver rejected the ride')
      toast.info('Ride rejected')
    } catch (error) {
      toast.error('Failed to reject ride')
    }
  }

  const handleStatusUpdate = async (orderId: string, status: OrderStatus) => {
    try {
      await updateOrderStatus(orderId, status)
      
      const statusMessages = {
        'driver_arriving': 'Marked as arriving to pickup',
        'driver_arrived': 'Marked as arrived at pickup',
        'picked_up': 'Passenger picked up',
        'in_progress': 'Trip started',
        'completed': 'Trip completed successfully'
      }
      
      toast.success(statusMessages[status] || 'Status updated')
    } catch (error) {
      toast.error('Failed to update status')
    }
  }

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'searching': return 'bg-blue-100 text-blue-800'
      case 'assigned': return 'bg-green-100 text-green-800'
      case 'driver_arriving': return 'bg-orange-100 text-orange-800'
      case 'driver_arrived': return 'bg-purple-100 text-purple-800'
      case 'picked_up': return 'bg-indigo-100 text-indigo-800'
      case 'in_progress': return 'bg-cyan-100 text-cyan-800'
      case 'completed': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getNextAction = (status: OrderStatus) => {
    switch (status) {
      case 'assigned': return { status: 'driver_arriving', text: 'Start Driving to Pickup' }
      case 'driver_arriving': return { status: 'driver_arrived', text: 'Mark as Arrived' }
      case 'driver_arrived': return { status: 'picked_up', text: 'Pick Up Passenger' }
      case 'picked_up': return { status: 'in_progress', text: 'Start Trip' }
      case 'in_progress': return { status: 'completed', text: 'Complete Trip' }
      default: return null
    }
  }

  if (!userProfile) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Driver Dashboard
          </h1>
          <p className="text-gray-600 mt-2">
            Welcome back, {userProfile.profile.firstName}!
          </p>
        </div>
        
        {/* Online/Offline Toggle */}
        <div className="flex items-center space-x-3">
          <span className="text-sm font-medium text-gray-700">
            {isOnline ? 'Online' : 'Offline'}
          </span>
          <button
            onClick={() => setIsOnline(!isOnline)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              isOnline ? 'bg-green-500' : 'bg-gray-300'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                isOnline ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="glass-effect rounded-2xl p-6 border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Today's Rides</p>
              <p className="text-2xl font-bold text-gray-900">12</p>
            </div>
            <Car className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        <div className="glass-effect rounded-2xl p-6 border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Today's Earnings</p>
              <p className="text-2xl font-bold text-gray-900">JD 85.50</p>
            </div>
            <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
              <span className="text-green-600 font-bold">JD</span>
            </div>
          </div>
        </div>
        <div className="glass-effect rounded-2xl p-6 border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Rating</p>
              <p className="text-2xl font-bold text-gray-900">4.8</p>
            </div>
            <div className="text-yellow-500">⭐</div>
          </div>
        </div>
        <div className="glass-effect rounded-2xl p-6 border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Hours Online</p>
              <p className="text-2xl font-bold text-gray-900">8.5h</p>
            </div>
            <Clock className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Active Order */}
      {activeOrder && (
        <div className="glass-effect rounded-2xl p-6 border border-white/20">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Active Ride</h2>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(activeOrder.status.current)}`}>
              {activeOrder.status.current.replace('_', ' ').toUpperCase()}
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Trip Details */}
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-3 h-3 rounded-full bg-green-500 mt-2"></div>
                <div>
                  <p className="text-sm text-gray-500">Pickup</p>
                  <p className="font-medium text-gray-900">{activeOrder.locations.pickup.address}</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-3 h-3 rounded-full bg-red-500 mt-2"></div>
                <div>
                  <p className="text-sm text-gray-500">Destination</p>
                  <p className="font-medium text-gray-900">{activeOrder.locations.destination.address}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Clock className="w-4 h-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Fare</p>
                  <p className="font-medium text-gray-900">JD {activeOrder.pricing.total.toFixed(2)}</p>
                </div>
              </div>
            </div>

            {/* Customer Details */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                  {activeOrder.customer.avatar ? (
                    <img 
                      src={activeOrder.customer.avatar} 
                      alt={activeOrder.customer.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  ) : (
                    <User className="w-6 h-6 text-blue-600" />
                  )}
                </div>
                <div>
                  <p className="font-medium text-gray-900">{activeOrder.customer.name}</p>
                  <p className="text-sm text-gray-500">Customer</p>
                </div>
              </div>

              <div className="flex space-x-2">
                <button className="flex-1 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors flex items-center justify-center">
                  <Phone className="w-4 h-4 mr-2" />
                  Call Customer
                </button>
                <button className="flex-1 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center justify-center">
                  <Navigation className="w-4 h-4 mr-2" />
                  Navigate
                </button>
              </div>

              {/* Status Update Button */}
              {(() => {
                const nextAction = getNextAction(activeOrder.status.current)
                return nextAction ? (
                  <button
                    onClick={() => handleStatusUpdate(activeOrder.id, nextAction.status)}
                    className="w-full bg-yellow-500 text-black px-4 py-2 rounded-lg hover:bg-yellow-600 transition-colors font-medium"
                  >
                    {nextAction.text}
                  </button>
                ) : null
              })()}
            </div>
          </div>
        </div>
      )}

      {/* Available Rides */}
      {isOnline && !activeOrder && (
        <div className="glass-effect rounded-2xl p-6 border border-white/20">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Available Rides</h2>
          
          {availableOrders.length === 0 ? (
            <div className="text-center py-8">
              <Car className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No rides available</p>
              <p className="text-sm text-gray-400">Stay online to receive ride requests</p>
            </div>
          ) : (
            <div className="space-y-4">
              {availableOrders.map((order) => (
                <div key={order.id} className="bg-white/50 rounded-xl p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                        <User className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{order.customer.name}</p>
                        <p className="text-sm text-gray-500">JD {order.pricing.total.toFixed(2)}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">Distance</p>
                      <p className="font-medium text-gray-900">2.5 km</p>
                    </div>
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-green-500"></div>
                      <p className="text-sm text-gray-700">{order.locations.pickup.address}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-red-500"></div>
                      <p className="text-sm text-gray-700">{order.locations.destination.address}</p>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleAcceptOrder(order.id)}
                      className="flex-1 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors flex items-center justify-center"
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Accept
                    </button>
                    <button
                      onClick={() => handleRejectOrder(order.id)}
                      className="flex-1 bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors flex items-center justify-center"
                    >
                      <XCircle className="w-4 h-4 mr-2" />
                      Decline
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Offline Message */}
      {!isOnline && (
        <div className="glass-effect rounded-2xl p-8 border border-white/20 text-center">
          <Car className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">You're Offline</h3>
          <p className="text-gray-600 mb-4">
            Turn on your availability to start receiving ride requests
          </p>
          <button
            onClick={() => setIsOnline(true)}
            className="bg-yellow-500 text-black px-6 py-3 rounded-xl font-semibold hover:bg-yellow-600 transition-colors"
          >
            Go Online
          </button>
        </div>
      )}
    </div>
  )
}
