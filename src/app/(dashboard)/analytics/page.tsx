'use client'

import { useAuth } from '@/hooks/useAuth'
import { BarChart3, TrendingUp, Users, Car, MapPin, Calendar, Download, Filter } from 'lucide-react'

export default function AnalyticsPage() {
  const { userProfile } = useAuth()

  if (!userProfile) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading analytics...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600 mt-2">Comprehensive insights into Yellow Taxi business performance</p>
        </div>
        <div className="flex space-x-3">
          <button className="px-6 py-3 rounded-xl bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all duration-300 flex items-center justify-center">
            <Filter className="w-5 h-5 mr-2" />
            Filters
          </button>
          <button className="px-6 py-3 rounded-xl bg-yellow-100 text-yellow-700 hover:bg-yellow-200 transition-all duration-300 flex items-center justify-center">
            <Download className="w-5 h-5 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Date Range Selector */}
      <div className="glass-effect rounded-2xl p-6 border border-white/20">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Time Period</h3>
          <div className="flex space-x-2">
            <button className="px-4 py-2 rounded-lg bg-yellow-500 text-black font-medium">Last 30 Days</button>
            <button className="px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all duration-300">Last 90 Days</button>
            <button className="px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all duration-300">Last Year</button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="glass-effect rounded-2xl p-6 border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">JD 186,404</p>
              <p className="text-sm text-green-600">+28% from last month</p>
            </div>
            <div className="w-12 h-12 rounded-xl bg-green-100 flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>
        <div className="glass-effect rounded-2xl p-6 border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Rides</p>
              <p className="text-2xl font-bold text-gray-900">1,247</p>
              <p className="text-sm text-green-600">+15% from last month</p>
            </div>
            <div className="w-12 h-12 rounded-xl bg-blue-100 flex items-center justify-center">
              <Car className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>
        <div className="glass-effect rounded-2xl p-6 border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Active Users</p>
              <p className="text-2xl font-bold text-gray-900">892</p>
              <p className="text-sm text-green-600">+12% from last month</p>
            </div>
            <div className="w-12 h-12 rounded-xl bg-purple-100 flex items-center justify-center">
              <Users className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
        <div className="glass-effect rounded-2xl p-6 border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Coverage Area</p>
              <p className="text-2xl font-bold text-gray-900">15</p>
              <p className="text-sm text-green-600">+2 new cities</p>
            </div>
            <div className="w-12 h-12 rounded-xl bg-yellow-100 flex items-center justify-center">
              <MapPin className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
        {/* Revenue Chart */}
        <div className="glass-effect rounded-2xl p-6 border border-white/20">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Revenue Trend</h3>
            <div className="flex space-x-2">
              <button className="px-3 py-1 rounded-lg bg-yellow-100 text-yellow-700 text-sm font-medium">Daily</button>
              <button className="px-3 py-1 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all duration-300 text-sm">Weekly</button>
            </div>
          </div>
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center text-gray-500">
              <BarChart3 className="w-12 h-12 mx-auto mb-2" />
              <p>Revenue chart will be displayed here</p>
            </div>
          </div>
        </div>

        {/* Rides Chart */}
        <div className="glass-effect rounded-2xl p-6 border border-white/20">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Rides by City</h3>
            <div className="flex space-x-2">
              <button className="px-3 py-1 rounded-lg bg-yellow-100 text-yellow-700 text-sm font-medium">Top 5</button>
              <button className="px-3 py-1 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all duration-300 text-sm">All Cities</button>
            </div>
          </div>
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center text-gray-500">
              <BarChart3 className="w-12 h-12 mx-auto mb-2" />
              <p>City rides chart will be displayed here</p>
            </div>
          </div>
        </div>
      </div>

      {/* Top Performers */}
      <div className="glass-effect rounded-2xl p-6 border border-white/20">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Top Performing Drivers</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-4 border border-yellow-200">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center">
                <span className="text-sm font-semibold text-yellow-800">MS</span>
              </div>
              <div>
                <p className="font-semibold text-gray-900">Mohammed Saleh</p>
                <p className="text-sm text-gray-500">Amman</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Rides</span>
                <span className="font-semibold">127</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Rating</span>
                <span className="font-semibold">4.9 ⭐</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Earnings</span>
                <span className="font-semibold">JD 3,250</span>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                <span className="text-sm font-semibold text-blue-800">AK</span>
              </div>
              <div>
                <p className="font-semibold text-gray-900">Ahmed Khalil</p>
                <p className="text-sm text-gray-500">Irbid</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Rides</span>
                <span className="font-semibold">98</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Rating</span>
                <span className="font-semibold">4.8 ⭐</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Earnings</span>
                <span className="font-semibold">JD 2,450</span>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                <span className="text-sm font-semibold text-green-800">OS</span>
              </div>
              <div>
                <p className="font-semibold text-gray-900">Omar Sami</p>
                <p className="text-sm text-gray-500">Zarqa</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Rides</span>
                <span className="font-semibold">89</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Rating</span>
                <span className="font-semibold">4.7 ⭐</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Earnings</span>
                <span className="font-semibold">JD 2,180</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Placeholder Content */}
      <div className="glass-effect rounded-2xl p-8 border border-white/20 text-center">
        <BarChart3 className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Analytics Dashboard</h3>
        <p className="text-gray-600 mb-4">
          This page will contain comprehensive analytics including:
        </p>
        <ul className="text-sm text-gray-500 space-y-1">
          <li>• Real-time business metrics and KPIs</li>
          <li>• Interactive charts and data visualizations</li>
          <li>• Driver performance analytics</li>
          <li>• Revenue and growth analysis</li>
          <li>• Geographic and demographic insights</li>
        </ul>
      </div>
    </div>
  )
}
