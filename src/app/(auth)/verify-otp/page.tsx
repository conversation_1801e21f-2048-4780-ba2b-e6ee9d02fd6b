"use client";

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { OTPVerification } from '@/components/auth/OTPVerification';
import { phoneAuthService } from '@/services/AuthService';
import { ConfirmationResult } from 'firebase/auth';

export default function VerifyOTPPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [phoneNumber, setPhoneNumber] = useState('');
  const [confirmationResult, setConfirmationResult] = useState<ConfirmationResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const handleInitialOTPRequest = useCallback(async (phone: string, isRetry: boolean = false) => {
    try {
      setIsLoading(true);
      setError('');
      

      const newConfirmation = await phoneAuthService.sendOTP(phone);
      setConfirmationResult(newConfirmation);
      
      // Store the session metadata
      sessionStorage.setItem('phoneAuth_confirmationResult', JSON.stringify({
        phoneNumber: phone,
        timestamp: Date.now(),
        hasActiveSession: true
      }));
      
      const message = isRetry ? 'New verification code sent successfully!' : 'Verification code sent successfully!';
      setSuccessMessage(message);
      setError('');
      console.log('OTP sent successfully', { isRetry });
      
      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage('');
      }, 5000);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setError(errorMessage);
      console.error('OTP request error:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    const phone = searchParams.get('phone');
    if (!phone) {
      router.push('/login');
      return;
    }
    setPhoneNumber(phone);
    
    // Check if we have a stored session to determine if this is a retry or initial request
    const storedAuth = sessionStorage.getItem('phoneAuth_confirmationResult');
    let isRetry = false;
    
    if (storedAuth) {
      try {
        const authData = JSON.parse(storedAuth);
        // Check if the stored auth is for the same phone number and not expired (5 minutes)
        if (authData.phoneNumber === phone && (Date.now() - authData.timestamp) < 5 * 60 * 1000) {
          // Valid recent session found - this is likely a retry after page refresh or navigation
          console.log('Found valid recent session, sending fresh OTP');
          isRetry = true;
        } else {
          // Clear expired or mismatched session
          sessionStorage.removeItem('phoneAuth_confirmationResult');
          console.log('Cleared expired or mismatched session');
        }
      } catch (error) {
        console.error('Error parsing stored auth data:', error);
        sessionStorage.removeItem('phoneAuth_confirmationResult');
      }
    }
    
    // Always request fresh OTP to ensure we have a valid confirmationResult
    // Firebase confirmationResult cannot be serialized/stored, so we need a fresh one
    handleInitialOTPRequest(phone, isRetry);
  }, [searchParams, router, handleInitialOTPRequest]);

  const handleVerify = async (code: string) => {
    if (!confirmationResult) {
      setError('No verification session found. Please request a new code.');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await phoneAuthService.verifyOTP(confirmationResult, code);
      
      // Clear the stored auth session on successful verification
      sessionStorage.removeItem('phoneAuth_confirmationResult');
      
      if (result.isNewUser) {
        // New user - redirect to complete profile
        router.push('/dashboard?welcome=true');
      } else {
        // Existing user - redirect to dashboard
        router.push('/dashboard');
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResend = async () => {
    try {
      setError('');
      setIsLoading(true);
      

      const newConfirmation = await phoneAuthService.sendOTP(phoneNumber);
      setConfirmationResult(newConfirmation);
      
      // Update the stored session
      sessionStorage.setItem('phoneAuth_confirmationResult', JSON.stringify({
        phoneNumber: phoneNumber,
        timestamp: Date.now(),
        hasActiveSession: true
      }));
      
      setSuccessMessage('New verification code sent successfully!');
      setError('');
      console.log('New OTP sent successfully via resend');
      
      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage('');
      }, 5000);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setError(errorMessage);
      console.error('Resend OTP error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    // Clear any stored auth session when going back
    sessionStorage.removeItem('phoneAuth_confirmationResult');
    router.push('/login');
  };

  // Cleanup function to clear session on unmount
  useEffect(() => {
    return () => {
      // Only clear if user is leaving the page without verification
      if (confirmationResult) {
        // Keep the session if user is still verifying
        return;
      }
      // Clear expired sessions
      const storedAuth = sessionStorage.getItem('phoneAuth_confirmationResult');
      if (storedAuth) {
        try {
          const authData = JSON.parse(storedAuth);
          if (Date.now() - authData.timestamp > 5 * 60 * 1000) {
            sessionStorage.removeItem('phoneAuth_confirmationResult');
          }
        } catch (error) {
          sessionStorage.removeItem('phoneAuth_confirmationResult');
        }
      }
    };
  }, [confirmationResult]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center">YellowTaxi</CardTitle>
          <CardDescription className="text-center">
            Verify your phone number to continue
          </CardDescription>
        </CardHeader>
        <CardContent>
          <OTPVerification
            phoneNumber={phoneNumber}
            confirmationResult={confirmationResult}
            onVerify={handleVerify}
            onResend={handleResend}
            onBack={handleBack}
            isLoading={isLoading}
            error={error}
            successMessage={successMessage}
          />
        </CardContent>
      </Card>
      

    </div>
  );
}
