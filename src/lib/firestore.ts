// lib/firestore.ts
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  onSnapshot,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore'
import { db } from './firebase'
import { UserDocument, OrderDocument, DriverDocument, PaymentDocument } from '@/types/database'

// Generic Firestore helpers
export class FirestoreService {
  // Create a new document
  static async create<T>(collectionName: string, data: Omit<T, 'id'>) {
    const docRef = await addDoc(collection(db, collectionName), {
      ...data,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    })
    return docRef.id
  }
  
  // Get a single document
  static async get<T>(collectionName: string, id: string): Promise<T | null> {
    const docRef = doc(db, collectionName, id)
    const docSnap = await getDoc(docRef)
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as T
    }
    return null
  }
  
  // Update a document
  static async update(collectionName: string, id: string, data: any) {
    const docRef = doc(db, collectionName, id)
    await updateDoc(docRef, {
      ...data,
      updatedAt: serverTimestamp()
    })
  }
  
  // Delete a document
  static async delete(collectionName: string, id: string) {
    const docRef = doc(db, collectionName, id)
    await deleteDoc(docRef)
  }
  
  // Get multiple documents with query
  static async getMany<T>(collectionName: string, constraints: any[] = []): Promise<T[]> {
    let q = collection(db, collectionName)
    
    if (constraints.length > 0) {
      q = query(q, ...constraints) as any
    }
    
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as T[]
  }
  
  // Listen to real-time updates
  static onSnapshot<T>(
    collectionName: string, 
    callback: (data: T[]) => void, 
    constraints: any[] = []
  ) {
    let q = collection(db, collectionName)
    
    if (constraints.length > 0) {
      q = query(q, ...constraints) as any
    }
    
    return onSnapshot(q, (snapshot) => {
      const data = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as T[]
      callback(data)
    })
  }
  
  // Listen to a single document
  static onDocSnapshot<T>(
    collectionName: string,
    id: string,
    callback: (data: T | null) => void
  ) {
    const docRef = doc(db, collectionName, id)
    return onSnapshot(docRef, (doc) => {
      if (doc.exists()) {
        callback({ id: doc.id, ...doc.data() } as T)
      } else {
        callback(null)
      }
    })
  }
}

// Specialized services for each collection
export class UserService extends FirestoreService {
  static async createUser(userData: Omit<UserDocument, 'id'>) {
    return this.create<UserDocument>('users', userData)
  }
  
  static async getUserById(id: string) {
    return this.get<UserDocument>('users', id)
  }
  
  static async updateUser(id: string, data: Partial<UserDocument>) {
    return this.update('users', id, data)
  }
  
  static async getUsersByRole(role: string) {
    return this.getMany<UserDocument>('users', [
      where('roles', 'array-contains', role)
    ])
  }
}

export class OrderService extends FirestoreService {
  static async createOrder(orderData: Omit<OrderDocument, 'id'>) {
    return this.create<OrderDocument>('orders', orderData)
  }
  
  static async getOrderById(id: string) {
    return this.get<OrderDocument>('orders', id)
  }
  
  static async updateOrder(id: string, data: Partial<OrderDocument>) {
    return this.update('orders', id, data)
  }
  
  static async getOrdersByCustomer(customerId: string) {
    return this.getMany<OrderDocument>('orders', [
      where('customer.id', '==', customerId),
      orderBy('metadata.createdAt', 'desc')
    ])
  }
  
  static async getOrdersByDriver(driverId: string) {
    return this.getMany<OrderDocument>('orders', [
      where('driver.id', '==', driverId),
      orderBy('metadata.createdAt', 'desc')
    ])
  }
  
  static async getActiveOrders() {
    return this.getMany<OrderDocument>('orders', [
      where('status.current', 'in', ['pending', 'searching', 'assigned', 'picked_up', 'in_progress'])
    ])
  }
}

export class DriverService extends FirestoreService {
  static async createDriver(driverData: Omit<DriverDocument, 'id'>) {
    return this.create<DriverDocument>('drivers', driverData)
  }
  
  static async getDriverById(id: string) {
    return this.get<DriverDocument>('drivers', id)
  }
  
  static async updateDriver(id: string, data: Partial<DriverDocument>) {
    return this.update('drivers', id, data)
  }
  
  static async getOnlineDrivers() {
    return this.getMany<DriverDocument>('drivers', [
      where('status.online', '==', true),
      where('status.available', '==', true)
    ])
  }
  
  static async getPendingDrivers() {
    return this.getMany<DriverDocument>('drivers', [
      where('verification.status', '==', 'pending')
    ])
  }
}

export class PaymentService extends FirestoreService {
  static async createPayment(paymentData: Omit<PaymentDocument, 'id'>) {
    return this.create<PaymentDocument>('payments', paymentData)
  }
  
  static async getPaymentById(id: string) {
    return this.get<PaymentDocument>('payments', id)
  }
  
  static async updatePayment(id: string, data: Partial<PaymentDocument>) {
    return this.update('payments', id, data)
  }
  
  static async getPaymentsByUser(userId: string) {
    return this.getMany<PaymentDocument>('payments', [
      where('userId', '==', userId),
      orderBy('metadata.createdAt', 'desc')
    ])
  }
}

// Utility functions
export function convertTimestamp(timestamp: any): Date {
  if (timestamp?.toDate) {
    return timestamp.toDate()
  }
  if (timestamp?.seconds) {
    return new Date(timestamp.seconds * 1000)
  }
  return new Date(timestamp)
}

export function createTimestamp(): Timestamp {
  return Timestamp.now()
}
