import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Country phone codes and validation patterns
 */
export interface CountryPhoneInfo {
  code: string;
  name: string;
  flag: string;
  pattern: RegExp;
  placeholder: string;
  example: string;
}

export const COUNTRY_PHONE_CODES: CountryPhoneInfo[] = [
  {
    code: '+962',
    name: 'Jordan',
    flag: '🇯🇴',
    pattern: /^7[0-9]{8}$/,
    placeholder: '7X XXX XXXX',
    example: '799123456'
  },
  {
    code: '+1',
    name: 'United States',
    flag: '🇺🇸',
    pattern: /^[2-9][0-9]{9}$/,
    placeholder: '(XXX) XXX-XXXX',
    example: '5551234567'
  },
  {
    code: '+44',
    name: 'United Kingdom',
    flag: '🇬🇧',
    pattern: /^[1-9][0-9]{9,10}$/,
    placeholder: '7XXX XXX XXX',
    example: '7911123456'
  },
  {
    code: '+49',
    name: 'Germany',
    flag: '🇩🇪',
    pattern: /^[1-9][0-9]{10,11}$/,
    placeholder: '1XX XXX XXXX',
    example: '15123456789'
  },
  {
    code: '+33',
    name: 'France',
    flag: '🇫🇷',
    pattern: /^[1-9][0-9]{8}$/,
    placeholder: '1 XX XX XX XX',
    example: '123456789'
  },
  {
    code: '+39',
    name: 'Italy',
    flag: '🇮🇹',
    pattern: /^[3][0-9]{9}$/,
    placeholder: '3XX XXX XXXX',
    example: '3123456789'
  },
  {
    code: '+34',
    name: 'Spain',
    flag: '🇪🇸',
    pattern: /^[6-9][0-9]{8}$/,
    placeholder: '6XX XXX XXX',
    example: '612345678'
  },
  {
    code: '+31',
    name: 'Netherlands',
    flag: '🇳🇱',
    pattern: /^[6][0-9]{8}$/,
    placeholder: '6XX XXX XXX',
    example: '612345678'
  },
  {
    code: '+46',
    name: 'Sweden',
    flag: '🇸🇪',
    pattern: /^[7][0-9]{8}$/,
    placeholder: '7XX XXX XXX',
    example: '712345678'
  },
  {
    code: '+47',
    name: 'Norway',
    flag: '🇳🇴',
    pattern: /^[4-9][0-9]{7}$/,
    placeholder: '4XX XXX XX',
    example: '41234567'
  },
  {
    code: '+45',
    name: 'Denmark',
    flag: '🇩🇰',
    pattern: /^[2-9][0-9]{7}$/,
    placeholder: '2XX XXX XX',
    example: '21234567'
  },
  {
    code: '+358',
    name: 'Finland',
    flag: '🇫🇮',
    pattern: /^[4-9][0-9]{8}$/,
    placeholder: '4XX XXX XXX',
    example: '412345678'
  },
  {
    code: '+971',
    name: 'UAE',
    flag: '🇦🇪',
    pattern: /^[5][0-9]{8}$/,
    placeholder: '5XX XXX XXX',
    example: '501234567'
  },
  {
    code: '+966',
    name: 'Saudi Arabia',
    flag: '🇸🇦',
    pattern: /^[5][0-9]{8}$/,
    placeholder: '5XX XXX XXX',
    example: '501234567'
  },
  {
    code: '+20',
    name: 'Egypt',
    flag: '🇪🇬',
    pattern: /^[1][0-9]{9}$/,
    placeholder: '1XX XXX XXXX',
    example: '1012345678'
  },
  {
    code: '+212',
    name: 'Morocco',
    flag: '🇲🇦',
    pattern: /^[6-7][0-9]{8}$/,
    placeholder: '6XX XXX XXX',
    example: '612345678'
  },
  {
    code: '+91',
    name: 'India',
    flag: '🇮🇳',
    pattern: /^[6-9][0-9]{9}$/,
    placeholder: '9XX XXX XXXX',
    example: '9876543210'
  },
  {
    code: '+86',
    name: 'China',
    flag: '🇨🇳',
    pattern: /^1[3-9][0-9]{9}$/,
    placeholder: '1XX XXXX XXXX',
    example: '***********'
  },
  {
    code: '+81',
    name: 'Japan',
    flag: '🇯🇵',
    pattern: /^[7-9][0-9]{8}$/,
    placeholder: '9X XXXX XXXX',
    example: '9012345678'
  },
  {
    code: '+82',
    name: 'South Korea',
    flag: '🇰🇷',
    pattern: /^1[0-9][0-9]{7,8}$/,
    placeholder: '10 XXXX XXXX',
    example: '1012345678'
  },
  {
    code: '+61',
    name: 'Australia',
    flag: '🇦🇺',
    pattern: /^[2-9][0-9]{8}$/,
    placeholder: '2XXX XXX XXX',
    example: '*********'
  },

];

/**
 * Validates phone number for a specific country
 * @param phone - Phone number to validate (without country code)
 * @param countryCode - Country code (e.g., '+962', '+1')
 * @returns true if valid, false otherwise
 */
export function isValidPhoneForCountry(phone: string, countryCode: string): boolean {
  const country = COUNTRY_PHONE_CODES.find(c => c.code === countryCode);
  if (!country) return false;
  
  // Remove any non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  return country.pattern.test(cleanPhone);
}

/**
 * Formats phone number to international format
 * @param phone - Phone number to format (without country code)
 * @param countryCode - Country code (e.g., '+962', '+1')
 * @returns Formatted phone number
 */
export function formatPhoneNumber(phone: string, countryCode: string): string {
  // Remove any non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  // If it's already in international format, return as is
  if (phone.startsWith('+')) {
    return phone;
  }
  
  // Add country code
  return `${countryCode}${cleanPhone}`;
}

/**
 * Gets country info by country code
 * @param countryCode - Country code (e.g., '+962', '+1')
 * @returns Country info or undefined if not found
 */
export function getCountryByCode(countryCode: string): CountryPhoneInfo | undefined {
  return COUNTRY_PHONE_CODES.find(c => c.code === countryCode);
}

/**
 * Gets placeholder text for a country
 * @param countryCode - Country code (e.g., '+962', '+1')
 * @returns Placeholder text or default
 */
export function getPhonePlaceholder(countryCode: string): string {
  const country = getCountryByCode(countryCode);
  return country?.placeholder || 'Enter phone number';
}

/**
 * Gets example phone number for a country
 * @param countryCode - Country code (e.g., '+962', '+1')
 * @returns Example phone number or empty string
 */
export function getPhoneExample(countryCode: string): string {
  const country = getCountryByCode(countryCode);
  return country?.example || '';
}
