// lib/validations.ts
import { z } from 'zod'

// User validation schemas
export const userProfileSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  phone: z.string().regex(/^\+[1-9]\d{1,14}$/, 'Invalid phone number format'),
  email: z.string().email('Invalid email format').optional(),
  gender: z.enum(['male', 'female']),
  birthDate: z.string().optional(),
  language: z.enum(['en', 'ar']).default('en')
})

export const userRegistrationSchema = z.object({
  ...userProfileSchema.shape,
  termsAccepted: z.boolean().refine(val => val === true, {
    message: 'You must accept the terms and conditions'
  })
})

// Authentication schemas
export const phoneAuthSchema = z.object({
  phoneNumber: z.string().regex(/^\+[1-9]\d{1,14}$/, 'Invalid phone number format')
})

export const otpVerificationSchema = z.object({
  code: z.string().length(6, 'OTP must be 6 digits'),
  phoneNumber: z.string()
})

export const emailAuthSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters')
})

// Driver validation schemas
export const driverPersonalInfoSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  phone: z.string().regex(/^\+[1-9]\d{1,14}$/, 'Invalid phone number format'),
  email: z.string().email('Invalid email format'),
  nationalId: z.string().min(1, 'National ID is required'),
  licenseNumber: z.string().min(1, 'License number is required'),
  licenseExpiry: z.string().refine(date => new Date(date) > new Date(), {
    message: 'License must not be expired'
  }),
  emergencyContact: z.object({
    name: z.string().min(1, 'Emergency contact name is required'),
    phone: z.string().regex(/^\+[1-9]\d{1,14}$/, 'Invalid phone number format'),
    relationship: z.string().optional()
  })
})

export const driverVehicleSchema = z.object({
  make: z.string().min(1, 'Vehicle make is required'),
  model: z.string().min(1, 'Vehicle model is required'),
  year: z.number().min(2000, 'Vehicle must be 2000 or newer').max(new Date().getFullYear() + 1),
  color: z.string().min(1, 'Vehicle color is required'),
  plateNumber: z.string().min(1, 'Plate number is required'),
  licenseNumber: z.string().min(1, 'Vehicle license number is required'),
  licenseExpiry: z.string().refine(date => new Date(date) > new Date(), {
    message: 'Vehicle license must not be expired'
  }),
  insurance: z.object({
    provider: z.string().min(1, 'Insurance provider is required'),
    policyNumber: z.string().min(1, 'Policy number is required'),
    expiryDate: z.string().refine(date => new Date(date) > new Date(), {
      message: 'Insurance must not be expired'
    })
  })
})

export const driverDocumentsSchema = z.object({
  nationalIdFront: z.string().min(1, 'National ID front image is required'),
  nationalIdBack: z.string().min(1, 'National ID back image is required'),
  drivingLicenseFront: z.string().min(1, 'Driving license front image is required'),
  drivingLicenseBack: z.string().min(1, 'Driving license back image is required'),
  vehicleLicense: z.string().min(1, 'Vehicle license image is required'),
  vehicleImages: z.array(z.string()).min(1, 'At least one vehicle image is required'),
  insuranceCertificate: z.string().min(1, 'Insurance certificate is required'),
  backgroundCheck: z.string().optional()
})

export const driverRegistrationSchema = z.object({
  personalInfo: driverPersonalInfoSchema,
  vehicle: driverVehicleSchema,
  documents: driverDocumentsSchema
})

// Order validation schemas
export const locationSchema = z.object({
  address: z.string().min(1, 'Address is required'),
  coordinates: z.object({
    latitude: z.number(),
    longitude: z.number()
  }),
  placeId: z.string(),
  details: z.object({
    city: z.string(),
    district: z.string(),
    building: z.string().optional(),
    floor: z.string().optional(),
    apartment: z.string().optional()
  }).optional()
})

export const orderCreationSchema = z.object({
  pickup: locationSchema,
  destination: locationSchema,
  categoryId: z.string().min(1, 'Service category is required'),
  paymentMethod: z.enum(['cash', 'card', 'wallet']),
  specialRequests: z.string().optional()
})

// Payment validation schemas
export const cardDetailsSchema = z.object({
  number: z.string().regex(/^\d{16}$/, 'Card number must be 16 digits'),
  expiryMonth: z.number().min(1).max(12),
  expiryYear: z.number().min(new Date().getFullYear()),
  cvv: z.string().regex(/^\d{3,4}$/, 'CVV must be 3 or 4 digits'),
  holderName: z.string().min(1, 'Cardholder name is required')
})

export const paymentProcessingSchema = z.object({
  orderId: z.string().min(1, 'Order ID is required'),
  amount: z.number().positive('Amount must be positive'),
  method: z.enum(['cash', 'card', 'wallet']),
  cardDetails: cardDetailsSchema.optional()
}).refine((data) => {
  if (data.method === 'card') {
    return !!data.cardDetails
  }
  return true
}, {
  message: 'Card details are required for card payments',
  path: ['cardDetails']
})

// Settings validation schemas
export const notificationSettingsSchema = z.object({
  orderUpdates: z.boolean(),
  promotions: z.boolean(),
  system: z.boolean()
})

export const privacySettingsSchema = z.object({
  shareLocation: z.boolean(),
  showProfile: z.boolean()
})

export const userSettingsSchema = z.object({
  notifications: notificationSettingsSchema,
  privacy: privacySettingsSchema,
  theme: z.enum(['light', 'dark'])
})

// Admin validation schemas
export const citySchema = z.object({
  name: z.string().min(1, 'City name is required'),
  country: z.string().min(1, 'Country is required'),
  coordinates: z.object({
    latitude: z.number(),
    longitude: z.number()
  }),
  isActive: z.boolean()
})

export const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  description: z.string().min(1, 'Description is required'),
  basePrice: z.number().positive('Base price must be positive'),
  pricePerKm: z.number().positive('Price per km must be positive'),
  pricePerMinute: z.number().positive('Price per minute must be positive'),
  isActive: z.boolean()
})

// Export types for use in components
export type UserProfileFormData = z.infer<typeof userProfileSchema>
export type UserRegistrationFormData = z.infer<typeof userRegistrationSchema>
export type PhoneAuthFormData = z.infer<typeof phoneAuthSchema>
export type OTPVerificationFormData = z.infer<typeof otpVerificationSchema>
export type EmailAuthFormData = z.infer<typeof emailAuthSchema>
export type DriverRegistrationFormData = z.infer<typeof driverRegistrationSchema>
export type OrderCreationFormData = z.infer<typeof orderCreationSchema>
export type PaymentProcessingFormData = z.infer<typeof paymentProcessingSchema>
export type UserSettingsFormData = z.infer<typeof userSettingsSchema>
