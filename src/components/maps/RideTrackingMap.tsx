'use client'

import { useEffect, useRef, useState } from 'react'
import { MapPin, Navigation, Car } from 'lucide-react'
import { useGoogleMaps, GoogleMapsUtils } from '@/lib/googleMaps'

interface Location {
  lat: number
  lng: number
}

interface RideTrackingMapProps {
  pickup: Location
  destination: Location
  driverLocation?: Location
  showRoute?: boolean
  className?: string
}

export function RideTrackingMap({
  pickup,
  destination,
  driverLocation,
  showRoute = true,
  className = ''
}: RideTrackingMapProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const [map, setMap] = useState<google.maps.Map | null>(null)
  const markersRef = useRef<google.maps.Marker[]>([])
  const routeRef = useRef<google.maps.DirectionsRenderer | null>(null)

  // Use the Google Maps hook
  const { isLoaded, isLoading, error } = useGoogleMaps()

  // Google Maps is loaded via the useGoogleMaps hook

  // Create map once when Google Maps has loaded
  useEffect(() => {
    if (!isLoaded || !mapRef.current || map) return

    // Check if Google Maps API is available
    if (typeof window !== 'undefined' && window.google && window.google.maps) {
      // Initialize real Google Maps
      const mapOptions: google.maps.MapOptions = {
        center: pickup,
        zoom: 13,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        disableDefaultUI: false,
        zoomControl: true,
        streetViewControl: false,
        fullscreenControl: false,
      }

      const googleMap = new google.maps.Map(mapRef.current, mapOptions)
      setMap(googleMap)
    } else {
      // Fallback to demo mode
      const mockMap = {
        setCenter: () => {},
        setZoom: () => {},
        addMarker: () => {},
        drawRoute: () => {}
      }
      setMap(mockMap as any)
    }
  }, [isLoaded, map])

  // Update markers and route when locations change
  useEffect(() => {
    if (!map || !window.google) return

    // Clear existing markers
    markersRef.current.forEach(marker => {
      marker.setMap(null)
    })
    markersRef.current = []

    // Add pickup marker
    const pickupMarker = new google.maps.Marker({
      position: pickup,
      map: map,
      title: 'Pickup Location',
      icon: {
        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
          <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="16" cy="16" r="12" fill="#10B981" stroke="white" stroke-width="2"/>
            <path d="M16 8L16 24M8 16L24 16" stroke="white" stroke-width="2" stroke-linecap="round"/>
          </svg>
        `),
        scaledSize: new google.maps.Size(32, 32),
        anchor: new google.maps.Point(16, 16)
      }
    })
    markersRef.current.push(pickupMarker)

    // Add destination marker
    const destinationMarker = new google.maps.Marker({
      position: destination,
      map: map,
      title: 'Destination',
      icon: {
        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
          <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="16" cy="16" r="12" fill="#EF4444" stroke="white" stroke-width="2"/>
            <path d="M12 16L14 18L20 12" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        `),
        scaledSize: new google.maps.Size(32, 32),
        anchor: new google.maps.Point(16, 16)
      }
    })
    markersRef.current.push(destinationMarker)

    // Add driver marker if available
    if (driverLocation) {
      const driverMarker = new google.maps.Marker({
        position: driverLocation,
        map: map,
        title: 'Driver Location',
        icon: {
          url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="16" cy="16" r="12" fill="#F59E0B" stroke="white" stroke-width="2"/>
              <rect x="10" y="12" width="12" height="8" rx="2" fill="white"/>
              <circle cx="13" cy="20" r="2" fill="white"/>
              <circle cx="19" cy="20" r="2" fill="white"/>
            </svg>
          `),
          scaledSize: new google.maps.Size(32, 32),
          anchor: new google.maps.Point(16, 16)
        }
      })
      markersRef.current.push(driverMarker)
    }

    // Fit map to show all markers
    const bounds = new google.maps.LatLngBounds()
    bounds.extend(pickup)
    bounds.extend(destination)
    if (driverLocation) {
      bounds.extend(driverLocation)
    }
    map.fitBounds(bounds)

    // Draw route if requested
    if (showRoute && routeRef.current) {
      routeRef.current.setMap(null)
    }

    if (showRoute) {
      const directionsService = new google.maps.DirectionsService()
      const directionsRenderer = new google.maps.DirectionsRenderer({
        suppressMarkers: true, // We're using custom markers
        polylineOptions: {
          strokeColor: '#3B82F6',
          strokeWeight: 4,
          strokeOpacity: 0.8
        }
      })

      directionsService.route({
        origin: pickup,
        destination: destination,
        travelMode: google.maps.TravelMode.DRIVING
      }, (result, status) => {
        if (status === google.maps.DirectionsStatus.OK && result) {
          directionsRenderer.setDirections(result)
          directionsRenderer.setMap(map)
          routeRef.current = directionsRenderer
        }
      })
    }
  }, [map, pickup, destination, driverLocation, showRoute])

  // For demo purposes, render a simple visual map
  return (
    <div className={`relative bg-gray-100 rounded-xl overflow-hidden ${className}`}>
      <div ref={mapRef} className="w-full h-full min-h-[300px] relative">
        {/* Demo Map Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-green-100">
          {/* Grid pattern to simulate map */}
          <div className="absolute inset-0 opacity-20">
            <svg width="100%" height="100%" className="w-full h-full">
              <defs>
                <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                  <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#94a3b8" strokeWidth="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>
          </div>

          {/* Pickup Location */}
          <div className="absolute top-1/4 left-1/4 transform -translate-x-1/2 -translate-y-1/2">
            <div className="bg-green-500 text-white p-2 rounded-full shadow-lg">
              <MapPin className="w-6 h-6" />
            </div>
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1">
              <div className="bg-white px-2 py-1 rounded shadow text-xs font-medium">
                Pickup
              </div>
            </div>
          </div>

          {/* Destination Location */}
          <div className="absolute bottom-1/4 right-1/4 transform translate-x-1/2 translate-y-1/2">
            <div className="bg-red-500 text-white p-2 rounded-full shadow-lg">
              <MapPin className="w-6 h-6" />
            </div>
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1">
              <div className="bg-white px-2 py-1 rounded shadow text-xs font-medium">
                Destination
              </div>
            </div>
          </div>

          {/* Driver Location (if available) */}
          {driverLocation && (
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div className="bg-yellow-500 text-black p-2 rounded-full shadow-lg animate-pulse">
                <Car className="w-6 h-6" />
              </div>
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1">
                <div className="bg-white px-2 py-1 rounded shadow text-xs font-medium">
                  Driver
                </div>
              </div>
            </div>
          )}

          {/* Route Line (demo) */}
          {showRoute && (
            <svg className="absolute inset-0 w-full h-full pointer-events-none">
              <path
                d="M 25% 25% Q 50% 50% 75% 75%"
                stroke="#3b82f6"
                strokeWidth="3"
                fill="none"
                strokeDasharray="5,5"
                className="animate-pulse"
              />
            </svg>
          )}
        </div>

        {/* Map Controls */}
        <div className="absolute top-4 right-4 space-y-2">
          <button className="bg-white p-2 rounded-lg shadow-md hover:shadow-lg transition-shadow">
            <Navigation className="w-5 h-5 text-gray-600" />
          </button>
          <button className="bg-white p-2 rounded-lg shadow-md hover:shadow-lg transition-shadow">
            <MapPin className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        {/* Loading State */}
        {!isLoaded && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
              <p className="mt-2 text-gray-600 text-sm">Loading map...</p>
            </div>
          </div>
        )}

        {/* API Status Notice */}
        <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm px-3 py-2 rounded-lg shadow-md">
          <p className="text-xs text-gray-600">
            {isLoaded && window.google?.maps ? (
              '🗺️ Google Maps Active'
            ) : error ? (
              '⚠️ Maps API Error - Demo Mode'
            ) : isLoading ? (
              '⏳ Loading Google Maps...'
            ) : (
              '📍 Demo Mode - Add API key for real maps'
            )}
          </p>
        </div>
      </div>
    </div>
  )
}

// Hook for real-time location tracking
export function useLocationTracking(orderId?: string) {
  const [driverLocation, setDriverLocation] = useState<Location | null>(null)
  const [isTracking, setIsTracking] = useState(false)

  useEffect(() => {
    if (!orderId) return

    // In a real app, you'd subscribe to real-time location updates here
    // For demo, simulate driver movement
    setIsTracking(true)
    
    const interval = setInterval(() => {
      // Simulate driver movement
      setDriverLocation(prev => {
        if (!prev) {
          return { lat: 31.9539, lng: 35.9106 } // Start at pickup area
        }
        
        // Simulate movement towards destination
        return {
          lat: prev.lat + (Math.random() - 0.5) * 0.001,
          lng: prev.lng + (Math.random() - 0.5) * 0.001
        }
      })
    }, 3000) // Update every 3 seconds

    return () => {
      clearInterval(interval)
      setIsTracking(false)
    }
  }, [orderId])

  return { driverLocation, isTracking }
}

export default RideTrackingMap
