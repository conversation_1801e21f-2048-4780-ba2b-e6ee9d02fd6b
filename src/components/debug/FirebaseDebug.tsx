"use client";

import { useState, useEffect } from 'react';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react';

export function FirebaseDebug() {
  const [connectionStatus, setConnectionStatus] = useState<'testing' | 'connected' | 'failed' | 'idle'>('idle');
  const [errorDetails, setErrorDetails] = useState<string>('');
  const [configStatus, setConfigStatus] = useState<any>(null);

  useEffect(() => {
    // Check configuration status
    const config = {
      hasApiKey: !!process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
      hasAuthDomain: !!process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
      hasProjectId: !!process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
    };
    setConfigStatus(config);
  }, []);

  const testFirebaseConnection = async () => {
    setConnectionStatus('testing');
    setErrorDetails('');

    try {
      // Try to read a simple document (this will fail gracefully if collection doesn't exist)
      const testDocRef = doc(db, 'test', 'connection');
      await getDoc(testDocRef);
      
      setConnectionStatus('connected');
    } catch (error: any) {
      console.error('Firebase connection test failed:', error);
      setConnectionStatus('failed');
      setErrorDetails(`${error.code}: ${error.message}`);
    }
  };

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'testing':
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'connected':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'border-green-200 bg-green-50';
      case 'failed':
        return 'border-red-200 bg-red-50';
      case 'testing':
        return 'border-blue-200 bg-blue-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <Card className={`w-full max-w-2xl ${getStatusColor()}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getStatusIcon()}
          Firebase Connection Debug
        </CardTitle>
        <CardDescription>
          Check your Firebase configuration and connection status
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Configuration Status */}
        <div className="space-y-2">
          <h3 className="font-medium">Configuration Status:</h3>
          {configStatus && (
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex items-center gap-2">
                {configStatus.hasApiKey ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                API Key: {configStatus.hasApiKey ? 'Set' : 'Missing'}
              </div>
              
              <div className="flex items-center gap-2">
                {configStatus.hasAuthDomain ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                Auth Domain: {configStatus.hasAuthDomain ? 'Set' : 'Missing'}
              </div>
              
              <div className="flex items-center gap-2">
                {configStatus.hasProjectId ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                Project ID: {configStatus.hasProjectId ? 'Set' : 'Missing'}
              </div>
            </div>
          )}
          
          {configStatus?.projectId && (
            <div className="text-sm text-gray-600">
              <strong>Project ID:</strong> {configStatus.projectId}
            </div>
          )}
          
          {configStatus?.authDomain && (
            <div className="text-sm text-gray-600">
              <strong>Auth Domain:</strong> {configStatus.authDomain}
            </div>
          )}
        </div>

        {/* Connection Test */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">Connection Test:</h3>
            <Button 
              onClick={testFirebaseConnection}
              disabled={connectionStatus === 'testing'}
              size="sm"
            >
              {connectionStatus === 'testing' ? 'Testing...' : 'Test Connection'}
            </Button>
          </div>
          
          {connectionStatus !== 'idle' && (
            <div className="p-3 rounded-md border">
              <div className="flex items-center gap-2 mb-2">
                {getStatusIcon()}
                <span className="font-medium">
                  {connectionStatus === 'testing' && 'Testing connection...'}
                  {connectionStatus === 'connected' && 'Connection successful!'}
                  {connectionStatus === 'failed' && 'Connection failed'}
                </span>
              </div>
              
              {errorDetails && (
                <div className="text-sm text-red-600 font-mono bg-red-50 p-2 rounded">
                  {errorDetails}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Common Issues */}
        <div className="space-y-2">
          <h3 className="font-medium">Common Issues:</h3>
          <div className="text-sm text-gray-600 space-y-1">
            <div>• Make sure your Firebase project exists and is active</div>
            <div>• Check that Firestore is enabled in your Firebase project</div>
            <div>• Verify environment variables are in <code>.env.local</code> (not <code>.env</code>)</div>
            <div>• Restart your development server after adding env variables</div>
            <div>• Check Firebase Console for any project issues</div>
          </div>
        </div>

        {/* Quick Links */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('https://console.firebase.google.com', '_blank')}
          >
            Open Firebase Console
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
          >
            Reload Page
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
