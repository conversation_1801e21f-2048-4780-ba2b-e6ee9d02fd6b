"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export function AuthFallback() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-amber-100">
            <AlertTriangle className="h-6 w-6 text-amber-600" />
          </div>
          <CardTitle className="text-xl">Setup Required</CardTitle>
          <CardDescription>
            Firebase authentication is not configured yet
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="rounded-lg bg-amber-50 p-4 border border-amber-200">
            <h3 className="font-medium text-amber-800 mb-2">
              To enable authentication:
            </h3>
            <ol className="text-sm text-amber-700 space-y-1 list-decimal list-inside">
              <li>Create a Firebase project</li>
              <li>Enable Authentication methods</li>
              <li>Add environment variables</li>
              <li>Restart the development server</li>
            </ol>
          </div>
          
          <div className="space-y-2">
            <Button
              className="w-full"
              onClick={() => window.open('https://console.firebase.google.com', '_blank')}
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              Open Firebase Console
            </Button>
            
            <Button
              variant="outline"
              className="w-full"
              onClick={() => window.open('https://firebase.google.com/docs/auth/web/start', '_blank')}
            >
              <Settings className="mr-2 h-4 w-4" />
              View Setup Guide
            </Button>
          </div>
          
          <div className="text-center">
            <Button
              variant="link"
              className="text-sm text-gray-600"
              onClick={() => window.location.reload()}
            >
              Refresh page after setup
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
