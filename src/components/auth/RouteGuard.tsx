"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/providers/AuthProvider';
import { UserRole } from '@/types/database';
import { Loader2 } from 'lucide-react';

interface RouteGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireRole?: UserRole;
  redirectTo?: string;
  fallback?: React.ReactNode;
}

export function RouteGuard({ 
  children, 
  requireAuth = true, 
  requireRole,
  redirectTo = '/login',
  fallback 
}: RouteGuardProps) {
  const { isAuthenticated, hasRole, loading, user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (loading) return; // Wait for auth state to load

    // Check authentication requirement
    if (requireAuth && !isAuthenticated) {
      router.push(redirectTo);
      return;
    }

    // Check role requirement
    if (requireRole && !hasRole(requireRole)) {
      // If user is authenticated but doesn't have required role
      if (isAuthenticated) {
        router.push('/dashboard'); // Redirect to default dashboard
      } else {
        router.push(redirectTo);
      }
      return;
    }
  }, [isAuthenticated, hasRole, loading, requireAuth, requireRole, router, redirectTo]);

  // Show loading while checking auth
  if (loading) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-yellow-600" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If auth is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    return null; // Will redirect via useEffect
  }

  // If specific role is required but user doesn't have it
  if (requireRole && !hasRole(requireRole)) {
    return null; // Will redirect via useEffect
  }

  // All checks passed, render children
  return <>{children}</>;
}

// Specific guard components for common use cases
export function RequireAuth({ children, redirectTo = '/login' }: { 
  children: React.ReactNode; 
  redirectTo?: string;
}) {
  return (
    <RouteGuard requireAuth={true} redirectTo={redirectTo}>
      {children}
    </RouteGuard>
  );
}

export function RequireRole({ 
  children, 
  role, 
  redirectTo = '/login' 
}: { 
  children: React.ReactNode; 
  role: UserRole;
  redirectTo?: string;
}) {
  return (
    <RouteGuard requireAuth={true} requireRole={role} redirectTo={redirectTo}>
      {children}
    </RouteGuard>
  );
}

export function RequireAdmin({ children }: { children: React.ReactNode }) {
  return (
    <RequireRole role="admin" redirectTo="/dashboard">
      {children}
    </RequireRole>
  );
}

export function RequireDriver({ children }: { children: React.ReactNode }) {
  return (
    <RequireRole role="driver" redirectTo="/dashboard">
      {children}
    </RequireRole>
  );
}

// Unauthorized access component
export function UnauthorizedAccess({ 
  message = "You don't have permission to access this page.",
  actionText = "Go to Dashboard",
  actionHref = "/dashboard"
}: {
  message?: string;
  actionText?: string;
  actionHref?: string;
}) {
  const router = useRouter();

  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <div className="text-center max-w-md">
        <div className="mb-4">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M12 15v2m0-6V7m0 10v-2m0-6V7m-6 8h12a2 2 0 002-2V7a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"
            />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Access Denied
        </h2>
        <p className="text-gray-600 mb-6">
          {message}
        </p>
        <button
          onClick={() => router.push(actionHref)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
        >
          {actionText}
        </button>
      </div>
    </div>
  );
}
