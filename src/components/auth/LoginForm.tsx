"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Phone, Mail, Eye, EyeOff, Loader2 } from "lucide-react";
import { phoneAuthService, socialAuthService, emailAuthService } from "@/services/AuthService";
import { FirebaseConfigWarning } from "./FirebaseConfigWarning";
import { isFirebaseConfigured } from "@/lib/firebase";

import { 
  isValidPhoneForCountry, 
  formatPhoneNumber, 
  COUNTRY_PHONE_CODES,
  getPhonePlaceholder,
  type CountryPhoneInfo
} from "@/lib/utils";

export function LoginForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSendingOTP, setIsSendingOTP] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [loginMethod, setLoginMethod] = useState<"phone" | "email">("phone");
  const [error, setError] = useState("");
  const [phone, setPhone] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [selectedCountry, setSelectedCountry] = useState<CountryPhoneInfo>(COUNTRY_PHONE_CODES[0]); // Default to Jordan
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isFirebaseConfigured) {
      setError("Firebase authentication is not configured. Please contact support.");
      return;
    }
    
    setIsLoading(true);
    setError("");
    
    try {
      if (loginMethod === "phone") {
        // Validate phone number
        if (!phone.trim()) {
          setError('Please enter your phone number.');
          return;
        }
        
        if (!isValidPhoneForCountry(phone, selectedCountry.code)) {
          setError(`Please enter a valid ${selectedCountry.name} phone number (e.g., ${selectedCountry.example}).`);
          return;
        }
        

        
        setIsSendingOTP(true);
        setError('');
        
        const fullPhone = formatPhoneNumber(phone, selectedCountry.code);
        await phoneAuthService.sendOTP(fullPhone);
        
        // Store session metadata for the verification page
        sessionStorage.setItem('phoneAuth_confirmationResult', JSON.stringify({
          phoneNumber: fullPhone,
          timestamp: Date.now(),
          hasActiveSession: true
        }));
        
        // Redirect to OTP verification page
        router.push(`/verify-otp?phone=${encodeURIComponent(fullPhone)}`);
      } else {
        // Email/password login
        await emailAuthService.signIn(email, password);
        router.push('/dashboard');
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
      setIsSendingOTP(false);
    }
  };

  const handleGoogleLogin = async () => {
    if (!isFirebaseConfigured) {
      setError("Firebase authentication is not configured. Please contact support.");
      return;
    }
    
    setIsLoading(true);
    setError("");
    
    try {
      await socialAuthService.signInWithGoogle();
      router.push('/dashboard');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!email) {
      setError("Please enter your email address first.");
      return;
    }

    try {
      await emailAuthService.resetPassword(email);
      setError(""); // Clear any previous errors
      alert("Password reset email sent! Check your inbox.");
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setError(errorMessage);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Login Method Toggle */}
      <div className="flex space-x-2 p-1 bg-gray-100 rounded-lg">
        <button
          type="button"
          onClick={() => {
            setLoginMethod("phone");
            setPassword(""); // Clear password when switching to phone
            setError(""); // Clear any previous errors
          }}
          className={`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${
            loginMethod === "phone"
              ? "bg-white text-gray-900 shadow-sm"
              : "text-gray-600 hover:text-gray-900"
          }`}
        >
          <Phone className="h-4 w-4 mr-2" />
          Phone
        </button>
        <button
          type="button"
          onClick={() => {
            setLoginMethod("email");
            setPhone(""); // Clear phone when switching to email
            setError(""); // Clear any previous errors
          }}
          className={`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${
            loginMethod === "email"
              ? "bg-white text-gray-900 shadow-sm"
              : "text-gray-600 hover:text-gray-900"
          }`}
        >
          <Mail className="h-4 w-4 mr-2" />
          Email
        </button>
      </div>

      {/* Login Fields */}
      {loginMethod === "phone" ? (
        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <div className="flex">
            {/* Country Selector */}
            <div className="relative">
              <select
                value={selectedCountry.code}
                onChange={(e) => {
                  const country = COUNTRY_PHONE_CODES.find(c => c.code === e.target.value);
                  if (country) {
                    setSelectedCountry(country);
                    setPhone(''); // Clear phone when country changes
                  }
                }}
                className="flex items-center px-3 py-2 border border-r-0 rounded-l-md bg-gray-50 text-sm text-gray-600 cursor-pointer hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 min-w-[120px]"
              >
                {COUNTRY_PHONE_CODES.map((country) => (
                  <option key={country.code} value={country.code}>
                    {country.flag} {country.code} ({country.name})
                  </option>
                ))}
              </select>
            </div>
            <Input
              id="phone"
              type="tel"
              placeholder={getPhonePlaceholder(selectedCountry.code)}
              className="rounded-l-none"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              required
            />
          </div>
          {/* Country Format Info */}
          <div className="text-xs text-gray-500">
            {selectedCountry.name} format: {selectedCountry.placeholder}
          </div>
          
          {phone && (
            <div className={`text-xs ${isValidPhoneForCountry(phone, selectedCountry.code) ? 'text-green-600' : 'text-red-600'}`}>
              {isValidPhoneForCountry(phone, selectedCountry.code) 
                ? `✓ Valid ${selectedCountry.name} phone number format` 
                : `Please enter a valid ${selectedCountry.name} phone number (e.g., ${selectedCountry.example})`
              }
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-2">
          <Label htmlFor="email">Email Address</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </div>
      )}

      {/* Password Field - Only show for email login */}
      {loginMethod === "email" && (
        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              placeholder="Enter your password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          </div>
        </div>
      )}

      {/* Forgot Password */}
      {loginMethod === "email" && (
        <div className="text-right">
          <Button 
            type="button"
            variant="link" 
            className="text-sm text-yellow-600 hover:text-yellow-700 p-0"
            onClick={handleForgotPassword}
          >
            Forgot password?
          </Button>
        </div>
      )}



      {/* Submit Button */}
      <Button
        type="submit"
        className="w-full bg-yellow-400 hover:bg-yellow-500 text-black"
        disabled={isLoading || isSendingOTP}
      >
        {isLoading || isSendingOTP ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            {loginMethod === "phone" ? "Sending OTP..." : "Signing in..."}
          </>
        ) : (
          loginMethod === "phone" ? "Send OTP" : "Sign In"
        )}
      </Button>

      {/* Error Message */}
      {error && (
        <div className="text-red-600 text-sm text-center bg-red-50 p-3 rounded-md">
          {error}
        </div>
      )}

      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <Separator />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-white px-2 text-gray-500">Or continue with</span>
        </div>
      </div>

      {/* Social Login Buttons */}
      <div className="w-full">
        <Button 
          variant="outline" 
          type="button" 
          className="w-full"
          onClick={handleGoogleLogin}
          disabled={isLoading || !isFirebaseConfigured}
        >
          <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="currentColor"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="currentColor"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="currentColor"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          Google
        </Button>
      </div>

      
      {/* Firebase Configuration Warning */}
      <FirebaseConfigWarning />
      

    </form>
  );
}
