"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/providers/AuthProvider';
import { UserRole } from '@/types/database';
import { RoleService } from '@/services/RoleService';
import { Loader2, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface PageGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  allowedRoles?: UserRole[];
  requiredPermissions?: Array<keyof import('@/services/RoleService').RolePermissions>;
  redirectTo?: string;
  fallback?: React.ReactNode;
  showUnauthorized?: boolean;
}

export function PageGuard({ 
  children, 
  requireAuth = true, 
  allowedRoles = [],
  requiredPermissions = [],
  redirectTo = '/login',
  fallback,
  showUnauthorized = true
}: PageGuardProps) {
  const { isAuthenticated, userProfile, loading, user } = useAuth();
  const router = useRouter();
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);

  useEffect(() => {
    if (loading) return;

    // Check authentication requirement
    if (requireAuth && !isAuthenticated) {
      router.push(redirectTo);
      return;
    }

    // If no auth required and user is not authenticated, allow access
    if (!requireAuth && !isAuthenticated) {
      setHasAccess(true);
      return;
    }

    // If user is authenticated but no profile exists yet, wait
    if (isAuthenticated && !userProfile) {
      setHasAccess(false);
      return;
    }

    // Check role requirements
    if (allowedRoles.length > 0 && userProfile) {
      const hasRole = RoleService.hasAnyRole(userProfile.roles, allowedRoles);
      if (!hasRole) {
        setHasAccess(false);
        return;
      }
    }

    // Check permission requirements
    if (requiredPermissions.length > 0 && userProfile) {
      const hasAllPermissions = requiredPermissions.every(permission =>
        RoleService.hasPermission(userProfile.roles, permission)
      );
      if (!hasAllPermissions) {
        setHasAccess(false);
        return;
      }
    }

    // All checks passed
    setHasAccess(true);
  }, [
    isAuthenticated, 
    userProfile, 
    loading, 
    requireAuth, 
    allowedRoles, 
    requiredPermissions, 
    router, 
    redirectTo
  ]);

  // Show loading while checking auth/permissions
  if (loading || hasAccess === null) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-yellow-600" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If auth is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    return null; // Will redirect via useEffect
  }

  // If user doesn't have required access
  if (!hasAccess) {
    if (showUnauthorized) {
      return <UnauthorizedPage userProfile={userProfile} />;
    }
    return null;
  }

  // All checks passed, render children
  return <>{children}</>;
}

// Unauthorized access page
function UnauthorizedPage({ userProfile }: { userProfile: any }) {
  const router = useRouter();
  const { signOut } = useAuth();

  const handleGoBack = () => {
    if (userProfile) {
      // Redirect based on user's primary role
      const primaryRole = RoleService.getPrimaryRole(userProfile.roles);
      switch (primaryRole) {
        case 'admin':
        case 'office_manager':
        case 'support':
        case 'driver':
          router.push('/dashboard');
          break;
        default:
          router.push('/');
      }
    } else {
      router.push('/');
    }
  };

  const handleSignOut = async () => {
    await signOut();
    router.push('/');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="text-center max-w-md">
        <div className="mb-6">
          <AlertCircle className="mx-auto h-16 w-16 text-red-500" />
        </div>
        
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Access Denied
        </h1>
        
        <p className="text-gray-600 mb-8">
          You don't have permission to access this page. Please contact your administrator 
          if you believe this is an error.
        </p>

        {userProfile && (
          <div className="bg-gray-100 rounded-lg p-4 mb-6">
            <p className="text-sm text-gray-700">
              <span className="font-medium">Current Role:</span>{' '}
              {RoleService.getRoleDisplayName(RoleService.getPrimaryRole(userProfile.roles))}
            </p>
          </div>
        )}

        <div className="space-y-3">
          <Button
            onClick={handleGoBack}
            className="w-full bg-yellow-600 hover:bg-yellow-700 text-white"
          >
            Go Back to Dashboard
          </Button>
          
          <Button
            onClick={handleSignOut}
            variant="outline"
            className="w-full"
          >
            Sign Out
          </Button>
        </div>
      </div>
    </div>
  );
}

// Specific guard components for common use cases

export function RequireAuth({ 
  children, 
  redirectTo = '/login',
  fallback 
}: { 
  children: React.ReactNode; 
  redirectTo?: string;
  fallback?: React.ReactNode;
}) {
  return (
    <PageGuard 
      requireAuth={true} 
      redirectTo={redirectTo}
      fallback={fallback}
    >
      {children}
    </PageGuard>
  );
}

export function RequireRoles({ 
  children, 
  roles,
  redirectTo = '/login',
  fallback 
}: { 
  children: React.ReactNode; 
  roles: UserRole[];
  redirectTo?: string;
  fallback?: React.ReactNode;
}) {
  return (
    <PageGuard 
      requireAuth={true} 
      allowedRoles={roles}
      redirectTo={redirectTo}
      fallback={fallback}
    >
      {children}
    </PageGuard>
  );
}

export function RequirePermissions({ 
  children, 
  permissions,
  redirectTo = '/login',
  fallback 
}: { 
  children: React.ReactNode; 
  permissions: Array<keyof import('@/services/RoleService').RolePermissions>;
  redirectTo?: string;
  fallback?: React.ReactNode;
}) {
  return (
    <PageGuard 
      requireAuth={true} 
      requiredPermissions={permissions}
      redirectTo={redirectTo}
      fallback={fallback}
    >
      {children}
    </PageGuard>
  );
}

export function RequireAdmin({ 
  children,
  fallback 
}: { 
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <RequireRoles roles={['admin']} fallback={fallback}>
      {children}
    </RequireRoles>
  );
}

export function RequireStaff({ 
  children,
  fallback 
}: { 
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <RequireRoles roles={['admin', 'office_manager', 'support']} fallback={fallback}>
      {children}
    </RequireRoles>
  );
}

export function RequireDriver({ 
  children,
  fallback 
}: { 
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <RequireRoles roles={['driver']} fallback={fallback}>
      {children}
    </RequireRoles>
  );
}

export function RequireDashboardAccess({ 
  children,
  fallback 
}: { 
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <RequirePermissions permissions={['canAccessDashboard']} fallback={fallback}>
      {children}
    </RequirePermissions>
  );
}
