"use client";

import { useState, useEffect } from 'react';
import { AlertTriangle, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { isFirebaseConfigured } from '@/lib/firebase';

export function FirebaseConfigWarning() {
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    // Check if user has already dismissed this warning
    const dismissed = localStorage.getItem('firebase-warning-dismissed');
    
    if (!isFirebaseConfigured && !dismissed) {
      setIsVisible(true);
    }
  }, []);

  const handleDismiss = () => {
    setIsDismissed(true);
    setIsVisible(false);
    localStorage.setItem('firebase-warning-dismissed', 'true');
  };

  const handleResetDismissal = () => {
    localStorage.removeItem('firebase-warning-dismissed');
    setIsDismissed(false);
    setIsVisible(true);
  };

  if (!isVisible || isDismissed) {
    return null;
  }

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-amber-100 border-b border-amber-200 p-4">
      <div className="max-w-7xl mx-auto flex items-start gap-3">
        <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
        
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium text-amber-800">
            Firebase Configuration Required
          </h3>
          <p className="mt-1 text-sm text-amber-700">
            Firebase authentication is not configured yet. To enable authentication features:
          </p>
          <ol className="mt-2 text-sm text-amber-700 list-decimal list-inside space-y-1">
            <li>Create a Firebase project at <a href="https://console.firebase.google.com" target="_blank" rel="noopener noreferrer" className="underline">Firebase Console</a></li>
            <li>Enable Authentication methods (Email/Password, Google, Facebook, Phone)</li>
            <li>Add your Firebase configuration to environment variables</li>
            <li>Update Firestore security rules</li>
          </ol>
          
          <div className="mt-3 flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              className="text-amber-800 border-amber-300 hover:bg-amber-50"
              onClick={() => window.open('https://firebase.google.com/docs/auth/web/start', '_blank')}
            >
              View Setup Guide
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="text-amber-800 border-amber-300 hover:bg-amber-50"
              onClick={() => window.open('https://console.firebase.google.com', '_blank')}
            >
              Open Firebase Console
            </Button>
          </div>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDismiss}
          className="text-amber-600 hover:text-amber-800 hover:bg-amber-50 p-1"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
