import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Clock, 
  Shield, 
  MapPin, 
  CreditCard, 
  Star, 
  Users 
} from "lucide-react";

export function FeaturesSection() {
  return (
    <section id="features" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Why Choose YellowTaxi?
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Experience the perfect blend of convenience, safety, and affordability
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <Card className="p-6 hover:shadow-lg transition-shadow">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
                <Clock className="h-6 w-6 text-yellow-600" />
              </div>
              <CardTitle>Quick Booking</CardTitle>
              <CardDescription>
                Book a ride in seconds with our intuitive app interface
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <Shield className="h-6 w-6 text-green-600" />
              </div>
              <CardTitle>Safe & Secure</CardTitle>
              <CardDescription>
                All drivers are verified and vehicles are regularly inspected
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <MapPin className="h-6 w-6 text-blue-600" />
              </div>
              <CardTitle>Real-time Tracking</CardTitle>
              <CardDescription>
                Track your ride in real-time and share trip details with family
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <CreditCard className="h-6 w-6 text-purple-600" />
              </div>
              <CardTitle>Flexible Payment</CardTitle>
              <CardDescription>
                Pay with cash, card, or digital wallet - whatever works for you
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                <Star className="h-6 w-6 text-orange-600" />
              </div>
              <CardTitle>Top-rated Drivers</CardTitle>
              <CardDescription>
                Professional drivers with excellent customer service ratings
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-red-600" />
              </div>
              <CardTitle>24/7 Support</CardTitle>
              <CardDescription>
                Round-the-clock customer support for all your needs
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </div>
    </section>
  );
}
