import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export function CTASection() {
  return (
    <section className="py-20 bg-gray-900">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
          Ready to Get Started?
        </h2>
        <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
          Join thousands of satisfied customers who choose YellowTaxi for their daily transportation needs.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" className="bg-yellow-400 hover:bg-yellow-500 text-black text-lg px-8" asChild>
            <Link href="/register" className="flex items-center">
              Sign Up Now
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
          <Button size="lg" variant="outline" className="text-lg px-8 text-black border-white hover:bg-white hover:text-black" asChild>
            <Link href="/login">I'm Already a Member</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
