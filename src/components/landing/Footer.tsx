import Link from "next/link";
import { Logo } from "@/components/ui/logo";

export function Footer() {
  return (
    <footer className="bg-white border-t py-12">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-4 gap-8">
          <div className="space-y-4">
            <Logo size="sm" />
            <p className="text-gray-600 text-sm">
              Your trusted ride partner in Jordan. Safe, reliable, and always on time.
            </p>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 mb-3">Company</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li><Link href="/about" className="hover:text-gray-900">About Us</Link></li>
              <li><Link href="/careers" className="hover:text-gray-900">Careers</Link></li>
              <li><Link href="/press" className="hover:text-gray-900">Press</Link></li>
              <li><Link href="/contact" className="hover:text-gray-900">Contact</Link></li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 mb-3">Services</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li><Link href="/ride" className="hover:text-gray-900">Book a Ride</Link></li>
              <li><Link href="/driver" className="hover:text-gray-900">Become a Driver</Link></li>
              <li><Link href="/business" className="hover:text-gray-900">Business</Link></li>
              <li><Link href="/support" className="hover:text-gray-900">Support</Link></li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 mb-3">Legal</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li><Link href="/privacy" className="hover:text-gray-900">Privacy Policy</Link></li>
              <li><Link href="/terms" className="hover:text-gray-900">Terms of Service</Link></li>
              <li><Link href="/safety" className="hover:text-gray-900">Safety</Link></li>
            </ul>
          </div>
        </div>

        <div className="border-t mt-8 pt-8 text-center text-sm text-gray-600">
          <p>&copy; 2024 YellowTaxi Jordan. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
