export function HowItWorksSection() {
  return (
    <section id="how-it-works" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            How It Works
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Getting your ride is as easy as 1-2-3
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-2xl font-bold text-black">1</span>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">Book Your Ride</h3>
            <p className="text-gray-600">
              Enter your pickup and destination locations in our easy-to-use app
            </p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-2xl font-bold text-black">2</span>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">Get Matched</h3>
            <p className="text-gray-600">
              We connect you with the nearest available driver in seconds
            </p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-2xl font-bold text-black">3</span>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">Enjoy the Ride</h3>
            <p className="text-gray-600">
              Sit back and relax while we get you to your destination safely
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
