import Image from 'next/image'

interface LogoProps {
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
  className?: string
}

export function Logo({ size = 'md', showText = true, className = '' }: LogoProps) {
  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-20 h-20',
    lg: 'w-24 h-24'
  }

  const textSizes = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl'
  }

  const flagSizes = {
    sm: 'w-5 h-3',
    md: 'w-6 h-4',
    lg: 'w-7 h-5'
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className={`${sizeClasses[size]} relative`}>
        <Image
          src="/logo.svg"
          alt="YellowTaxi Logo"
          fill
          className="object-contain"
          priority
        />
      </div>
      {showText && (
        <div className="flex items-center space-x-2">
          <span className={`font-bold text-gray-900 ${textSizes[size]}`}>
            YellowTaxi
          </span>
          {/* Jordan Flag */}
          <div className={`${flagSizes[size]} flex-shrink-0`}>
            <svg viewBox="0 0 1200 600" className="w-full h-full">
              {/* Black stripe */}
              <rect x="0" y="0" width="1200" height="200" fill="#000000"/>
              {/* White stripe */}
              <rect x="0" y="200" width="1200" height="200" fill="#FFFFFF"/>
              {/* Green stripe */}
              <rect x="0" y="400" width="1200" height="200" fill="#007A3D"/>
              {/* Red triangle */}
              <polygon points="0,0 400,300 0,600" fill="#CE1126"/>
              {/* White star */}
              <polygon 
                points="200,200 220,240 260,240 230,270 240,310 200,290 160,310 170,270 140,240 180,240" 
                fill="#FFFFFF"
              />
            </svg>
          </div>
        </div>
      )}
    </div>
  )
}
