"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from 'firebase/auth';
import { doc, onSnapshot } from 'firebase/firestore';
import { auth, db, isFirebaseConfigured } from '@/lib/firebase';
import { UserDocument, UserRole } from '@/types/database';

interface AuthContextType {
  user: User | null;
  userProfile: UserDocument | null;
  loading: boolean;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
  hasRole: (role: UserRole) => boolean;
  isAdmin: boolean;
  error: string | null;
  refreshUserProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Listen to Firebase auth state changes
  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(
      (user) => {
        setUser(user);
        setError(null);
        
        if (!user) {
          setUserProfile(null);
          setLoading(false);
        }
      },
      (error) => {
        console.error('Auth state change error:', error);
        setError(error.message);
        setLoading(false);
      }
    );

    return unsubscribe;
  }, []);

  // Listen to user profile changes in Firestore
  useEffect(() => {
    if (!user) {
      setUserProfile(null);
      return;
    }

    if (!isFirebaseConfigured) {
      console.warn('Firebase not configured, skipping Firestore operations');
      setUserProfile(null);
      setLoading(false);
      return;
    }

    const userDocRef = doc(db, 'users', user.uid);
    const unsubscribe = onSnapshot(
      userDocRef,
      (doc) => {
        if (doc.exists()) {
          const data = doc.data();
          setUserProfile({
            id: doc.id,
            ...data
          } as UserDocument);
        } else {
          setUserProfile(null);
        }
        setLoading(false);
      },
      (error) => {
        console.error('User profile listener error:', error);
        
        // Handle specific Firebase errors
        if (process.env.NODE_ENV === 'development') {
          console.error('🔥 Firebase Error Details:', {
            code: error.code,
            message: error.message,
            customData: error.customData
          });
        }

        if (error.code === 'unavailable') {
          setError('Firebase is currently offline. Please check your internet connection and Firebase project status.');
        } else if (error.code === 'permission-denied') {
          setError('Access denied. Please check your Firebase security rules and authentication.');
        } else if (error.code === 'not-found') {
          setError('Firebase project not found. Please verify your Firebase configuration.');
        } else if (error.message.includes('offline')) {
          setError('Firebase connection failed. Please check your Firebase project exists and is active.');
        } else {
          setError(`Firebase error: ${error.message}`);
        }
        
        setLoading(false);
      }
    );

    return unsubscribe;
  }, [user]);

  const signOut = async () => {
    try {
      await auth.signOut();
      setUser(null);
      setUserProfile(null);
      setError(null);
    } catch (error: any) {
      console.error('Sign out error:', error);
      setError(error.message);
    }
  };

  const refreshUserProfile = async () => {
    if (!user) return;
    
    // The real-time listener will automatically update the profile
    // This function is here for manual refresh if needed
  };

  const hasRole = (role: UserRole): boolean => {
    return userProfile?.roles.includes(role) || false;
  };

  const isAdmin = hasRole('admin');
  const isAuthenticated = !!user;

  const value: AuthContextType = {
    user,
    userProfile,
    loading,
    signOut,
    isAuthenticated,
    hasRole,
    isAdmin,
    error,
    refreshUserProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Role-based access control hooks
export function useRequireAuth() {
  const { isAuthenticated, loading } = useAuth();
  
  if (!loading && !isAuthenticated) {
    throw new Error('Authentication required');
  }
  
  return { isAuthenticated, loading };
}

export function useRequireRole(requiredRole: UserRole) {
  const { hasRole, loading } = useAuth();
  
  if (!loading && !hasRole(requiredRole)) {
    throw new Error(`Role '${requiredRole}' required`);
  }
  
  return { hasRole: hasRole(requiredRole), loading };
}

export function useRequireAdmin() {
  return useRequireRole('admin');
}
