'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Bell, Settings, LogOut, User, Shield, Search, ChevronDown } from 'lucide-react'

export function DashboardHeader() {
  const { user, userProfile, signOut } = useAuth()
  const [notificationsOpen, setNotificationsOpen] = useState(false)
  const router = useRouter()

  if (!user || !userProfile) {
    return null
  }

  const getInitials = () => {
    const firstName = userProfile.profile.firstName || ''
    const lastName = userProfile.profile.lastName || ''
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || 'U'
  }

  const getPrimaryRole = () => {
    if (userProfile.roles.includes('admin')) return 'Administrator'
    if (userProfile.roles.includes('driver')) return 'Driver'
    if (userProfile.roles.includes('office_manager')) return 'Office Manager'
    if (userProfile.roles.includes('support')) return 'Support'
    return 'Customer'
  }

  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Good morning'
    if (hour < 17) return 'Good afternoon'
    return 'Good evening'
  }

  return (
    <header className="opacity-0 animate-slide-down delay-100 flex flex-col lg:flex-row lg:items-center justify-between mb-10">
      <div>
        <h1 className="text-3xl lg:text-4xl tracking-tight bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-2 font-semibold">
          {getGreeting()}, {userProfile.profile.firstName} 🚕
        </h1>
        <button className="flex items-center text-gray-500 group transition-all duration-300 hover:text-gray-700">
          <span className="text-base font-medium">Your taxi business overview</span>
          <ChevronDown className="w-5 h-5 ml-2 group-hover:translate-y-1 transition-transform duration-300" strokeWidth={1.5} />
        </button>
      </div>
      
      <div className="flex items-center space-x-4 mt-6 lg:mt-0">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-4 top-3.5 w-5 h-5 text-gray-400" strokeWidth={1.5} />
          <input 
            type="text" 
            placeholder="Search rides, drivers..."
            className="pl-12 pr-4 py-3 w-72 rounded-2xl border-0 glass-effect shadow-lg focus:shadow-xl focus:outline-none focus:ring-2 focus:ring-yellow-500 text-sm transition-all duration-300 placeholder-gray-400"
          />
        </div>
        
        {/* Notifications */}
        <button 
          onClick={() => setNotificationsOpen(!notificationsOpen)}
          className="p-3 rounded-2xl glass-effect shadow-lg hover:shadow-xl transition-all duration-300 relative group transform hover:scale-105"
        >
          <Bell className="w-5 h-5 text-gray-600 group-hover:text-yellow-600 transition-colors duration-300" strokeWidth={1.5} />
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-red-500 to-pink-500 rounded-full animate-pulse shadow-lg"></div>
        </button>

        {/* User Profile */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div className="flex items-center space-x-3 glass-effect px-4 py-2 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 cursor-pointer">
              <Avatar className="w-12 h-12 border-white border-2 rounded-xl shadow-md">
                <AvatarImage src={userProfile.profile.avatar} alt={userProfile.profile.firstName} />
                <AvatarFallback className="bg-yellow-500 text-black font-semibold">
                  {getInitials()}
                </AvatarFallback>
              </Avatar>
              <div className="hidden md:block">
                <p className="text-sm font-semibold text-gray-900">
                  {userProfile.profile.firstName} {userProfile.profile.lastName}
                </p>
                <p className="text-xs text-gray-500 font-medium">
                  {getPrimaryRole()}
                </p>
              </div>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56 glass-effect border-white/20">
            <DropdownMenuLabel>
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">
                  {userProfile.profile.firstName} {userProfile.profile.lastName}
                </p>
                <p className="text-xs leading-none text-muted-foreground">
                  {userProfile.profile.email || userProfile.profile.phone}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => router.push('/profile')}>
              <User className="mr-2 h-4 w-4" />
              Profile
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => router.push('/settings')}>
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </DropdownMenuItem>
            {userProfile.roles.includes('admin') && (
              <DropdownMenuItem onClick={() => router.push('/admin')}>
                <Shield className="mr-2 h-4 w-4" />
                Admin Panel
              </DropdownMenuItem>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={signOut}>
              <LogOut className="mr-2 h-4 w-4" />
              Sign Out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}