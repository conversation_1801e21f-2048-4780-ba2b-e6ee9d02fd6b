'use client'

import React from 'react'
import { DriverUser } from '@/hooks/useDrivers'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Edit, 
  Eye, 
  CheckCircle, 
  XCircle, 
  Clock,
  User,
  Car,
  FileText,
  AlertCircle
} from 'lucide-react'

interface DriverUsersTableProps {
  driverUsers: DriverUser[]
  loading: boolean
  onEditDriver: (driverUser: DriverUser) => void
  onViewDriver: (driverUser: DriverUser) => void
  onVerifyDriver: (driverUser: DriverUser) => void
}

export function DriverUsersTable({
  driverUsers,
  loading,
  onEditDriver,
  onViewDriver,
  onVerifyDriver
}: DriverUsersTableProps) {
  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="rounded-full bg-gray-200 h-10 w-10"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (driverUsers.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
        <div className="p-12 text-center">
          <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Driver Users Found</h3>
          <p className="text-gray-500">
            No users with driver role found. Create users with driver role in User Management first.
          </p>
        </div>
      </div>
    )
  }

  const getVerificationBadge = (driverUser: DriverUser) => {
    if (!driverUser.hasDriverInfo) {
      return (
        <Badge variant="secondary" className="bg-gray-100 text-gray-700">
          <AlertCircle className="w-3 h-3 mr-1" />
          No Driver Info
        </Badge>
      )
    }

    const status = driverUser.driverDoc?.verification.status
    switch (status) {
      case 'approved':
        return (
          <Badge variant="default" className="bg-green-100 text-green-700">
            <CheckCircle className="w-3 h-3 mr-1" />
            Approved
          </Badge>
        )
      case 'rejected':
        return (
          <Badge variant="destructive" className="bg-red-100 text-red-700">
            <XCircle className="w-3 h-3 mr-1" />
            Rejected
          </Badge>
        )
      case 'pending':
      default:
        return (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-700">
            <Clock className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        )
    }
  }

  const getStatusBadge = (driverUser: DriverUser) => {
    if (!driverUser.hasDriverInfo) {
      return (
        <Badge variant="secondary" className="bg-gray-100 text-gray-700">
          Inactive
        </Badge>
      )
    }

    const isOnline = driverUser.driverDoc?.status.online
    const isAvailable = driverUser.driverDoc?.status.available

    if (isOnline && isAvailable) {
      return (
        <Badge variant="default" className="bg-green-100 text-green-700">
          Available
        </Badge>
      )
    } else if (isOnline) {
      return (
        <Badge variant="secondary" className="bg-yellow-100 text-yellow-700">
          Busy
        </Badge>
      )
    } else {
      return (
        <Badge variant="secondary" className="bg-gray-100 text-gray-700">
          Offline
        </Badge>
      )
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Driver
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Driver Info
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Verification
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {driverUsers.map((driverUser) => (
              <tr key={driverUser.user.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-full bg-gradient-to-r from-yellow-400 to-yellow-600 flex items-center justify-center">
                        <span className="text-sm font-medium text-black">
                          {driverUser.user.profile?.firstName?.[0] || 'D'}
                          {driverUser.user.profile?.lastName?.[0] || 'U'}
                        </span>
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        {driverUser.user.profile?.firstName} {driverUser.user.profile?.lastName}
                      </div>
                      <div className="text-sm text-gray-500">
                        ID: {driverUser.user.id.slice(0, 8)}...
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{driverUser.user.profile?.phone}</div>
                  <div className="text-sm text-gray-500">{driverUser.user.profile?.email}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {driverUser.hasDriverInfo ? (
                    <div className="flex items-center text-sm text-gray-900">
                      <Car className="w-4 h-4 mr-2 text-green-500" />
                      <div>
                        <div>{driverUser.driverDoc?.vehicle.make} {driverUser.driverDoc?.vehicle.model}</div>
                        <div className="text-gray-500">{driverUser.driverDoc?.vehicle.plateNumber}</div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center text-sm text-gray-500">
                      <AlertCircle className="w-4 h-4 mr-2 text-orange-500" />
                      No vehicle info
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getVerificationBadge(driverUser)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(driverUser)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onViewDriver(driverUser)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditDriver(driverUser)}
                      className="text-yellow-600 hover:text-yellow-900"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    {driverUser.hasDriverInfo && driverUser.driverDoc?.verification.status === 'pending' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onVerifyDriver(driverUser)}
                        className="text-green-600 hover:text-green-900"
                      >
                        <FileText className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
