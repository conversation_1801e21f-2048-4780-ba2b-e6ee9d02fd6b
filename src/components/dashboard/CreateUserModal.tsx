import React, { useState } from 'react';
import { X, UserPlus, Save, Loader2 } from 'lucide-react';
import { CreateUserData, UserRole } from '@/types/database';
import { RoleService } from '@/services/RoleService';

interface CreateUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateUser: (userData: CreateUserData) => Promise<string>;
}

export const CreateUserModal: React.FC<CreateUserModalProps> = ({
  isOpen,
  onClose,
  onCreateUser
}) => {
  const [formData, setFormData] = useState<CreateUserData>({
    profile: {
      firstName: '',
      lastName: '',
      phone: '',
      email: '',
      gender: 'male',
      birthDate: '',
      language: 'en'
    },
    roles: ['customer'],
    authentication: {
      phoneVerified: false,
      emailVerified: false,
      providers: ['phone'],
      password: ''
    }
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [section, key] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [section]: {
          ...prev[section as keyof CreateUserData],
          [key]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.profile.firstName.trim()) {
      newErrors['profile.firstName'] = 'First name is required';
    }

    if (!formData.profile.lastName.trim()) {
      newErrors['profile.lastName'] = 'Last name is required';
    }

    if (!formData.profile.phone.trim()) {
      newErrors['profile.phone'] = 'Phone number is required';
    } else if (!/^\+?[1-9]\d{1,14}$/.test(formData.profile.phone)) {
      newErrors['profile.phone'] = 'Invalid phone number format';
    }

    if (formData.profile.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.profile.email)) {
      newErrors['profile.email'] = 'Invalid email format';
    }

    // Password validation when email is provided
    if (formData.profile.email && (!formData.authentication.password || formData.authentication.password.length < 6)) {
      newErrors['authentication.password'] = 'Password is required and must be at least 6 characters long';
    }

    if (!formData.roles || formData.roles.length === 0) {
      newErrors['roles'] = 'At least one role is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onCreateUser(formData);
      onClose();
      // Reset form
      setFormData({
        profile: {
          firstName: '',
          lastName: '',
          phone: '',
          email: '',
          gender: 'male',
          birthDate: '',
          language: 'en'
        },
        roles: ['customer'],
        authentication: {
          phoneVerified: false,
          emailVerified: false,
          providers: ['phone']
        }
      });
      setErrors({});
    } catch (error) {
      console.error('Failed to create user:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-opacity-10 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <UserPlus className="w-6 h-6 text-yellow-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Create New User</h2>
              <p className="text-sm text-gray-500">Add a new user to the system</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Personal Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  First Name *
                </label>
                <input
                  type="text"
                  value={formData.profile.firstName}
                  onChange={(e) => handleInputChange('profile.firstName', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 ${
                    errors['profile.firstName'] ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter first name"
                />
                {errors['profile.firstName'] && (
                  <p className="text-red-500 text-sm mt-1">{errors['profile.firstName']}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Last Name *
                </label>
                <input
                  type="text"
                  value={formData.profile.lastName}
                  onChange={(e) => handleInputChange('profile.lastName', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 ${
                    errors['profile.lastName'] ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter last name"
                />
                {errors['profile.lastName'] && (
                  <p className="text-red-500 text-sm mt-1">{errors['profile.lastName']}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  value={formData.profile.phone}
                  onChange={(e) => handleInputChange('profile.phone', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 ${
                    errors['profile.phone'] ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="+1234567890"
                />
                {errors['profile.phone'] && (
                  <p className="text-red-500 text-sm mt-1">{errors['profile.phone']}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  value={formData.profile.email}
                  onChange={(e) => handleInputChange('profile.email', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 ${
                    errors['profile.email'] ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="<EMAIL>"
                />
                {errors['profile.email'] && (
                  <p className="text-red-500 text-sm mt-1">{errors['profile.email']}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Gender
                </label>
                <select
                  value={formData.profile.gender}
                  onChange={(e) => handleInputChange('profile.gender', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                >
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Language
                </label>
                <select
                  value={formData.profile.language}
                  onChange={(e) => handleInputChange('profile.language', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                >
                  <option value="en">English</option>
                  <option value="ar">Arabic</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Birth Date
                </label>
                <input
                  type="date"
                  value={formData.profile.birthDate}
                  onChange={(e) => handleInputChange('profile.birthDate', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                />
              </div>
            </div>
          </div>

          {/* Roles and Permissions */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Roles and Permissions</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  User Roles *
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {(['customer', 'driver', 'admin', 'office_manager', 'support'] as UserRole[]).map((role) => (
                    <label key={role} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.roles.includes(role)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            handleInputChange('roles', [...formData.roles, role]);
                          } else {
                            handleInputChange('roles', formData.roles.filter(r => r !== role));
                          }
                        }}
                        className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
                      />
                      <span className="text-sm text-gray-700">
                        {RoleService.getRoleDisplayName(role)}
                      </span>
                    </label>
                  ))}
                </div>
                {errors['roles'] && (
                  <p className="text-red-500 text-sm mt-1">{errors['roles']}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Authentication Settings
                </label>
                <div className="space-y-3">
                  {/* Password field for email authentication */}
                  {formData.profile.email && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Password *
                      </label>
                      <input
                        type="password"
                        value={formData.authentication.password || ''}
                        onChange={(e) => handleInputChange('authentication.password', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 ${
                          errors['authentication.password'] ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Enter password for email authentication"
                        minLength={6}
                      />
                      {errors['authentication.password'] && (
                        <p className="text-red-500 text-sm mt-1">{errors['authentication.password']}</p>
                      )}
                    </div>
                  )}
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.authentication.phoneVerified}
                      onChange={(e) => handleInputChange('authentication.phoneVerified', e.target.checked)}
                      className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
                    />
                    <span className="text-sm text-gray-700">Phone number verified</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.authentication.emailVerified}
                      onChange={(e) => handleInputChange('authentication.emailVerified', e.target.checked)}
                      className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
                    />
                    <span className="text-sm text-gray-700">Email verified</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-yellow-500 text-black font-semibold rounded-lg hover:bg-yellow-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Creating...</span>
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  <span>Create User</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
