import React, { memo } from 'react';
import Image from 'next/image';
import { 
  X, 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  Shield, 
  Settings, 
  BarChart3,
  Globe,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { UserDocument, UserRole } from '@/types/database';

interface ViewUserModalProps {
  isOpen: boolean;
  user: UserDocument | null;
  onClose: () => void;
}

// Memoize the StatusBadge component
const StatusBadge = memo<{ status: UserDocument['status'] }>(({ status }) => {
  const getStatusConfig = (status: UserDocument['status']) => {
    switch (status) {
      case 'active':
        return { color: 'bg-green-100 text-green-800 border-green-200', text: 'Active', icon: CheckCircle };
      case 'inactive':
        return { color: 'bg-gray-100 text-gray-800 border-gray-200', text: 'Inactive', icon: AlertCircle };
      case 'suspended':
        return { color: 'bg-red-100 text-red-800 border-red-200', text: 'Suspended', icon: XCircle };
      default:
        return { color: 'bg-gray-100 text-gray-800 border-gray-200', text: 'Unknown', icon: AlertCircle };
    }
  };

  const config = getStatusConfig(status);
  const IconComponent = config.icon;

  return (
    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${config.color}`}>
      <IconComponent className="w-4 h-4 mr-2" />
      {config.text}
    </div>
  );
});

StatusBadge.displayName = 'StatusBadge';

// Memoize the RoleBadge component
const RoleBadge = memo<{ role: UserRole }>(({ role }) => {
  const getRoleConfig = (role: UserRole) => {
    switch (role) {
      case 'customer':
        return { color: 'bg-blue-100 text-blue-800 border-blue-200', text: 'Customer' };
      case 'driver':
        return { color: 'bg-green-100 text-green-800 border-green-200', text: 'Driver' };
      case 'admin':
        return { color: 'bg-purple-100 text-purple-800 border-purple-200', text: 'Admin' };
      case 'office_manager':
        return { color: 'bg-orange-100 text-orange-800 border-orange-200', text: 'Office Manager' };
      case 'support':
        return { color: 'bg-teal-100 text-teal-800 border-teal-200', text: 'Support' };
      default:
        return { color: 'bg-gray-100 text-gray-800 border-gray-200', text: 'Unknown' };
    }
  };

  const config = getRoleConfig(role);

  return (
    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${config.color}`}>
      {config.text}
    </span>
  );
});

RoleBadge.displayName = 'RoleBadge';

// Memoize the VerificationBadge component
const VerificationBadge = memo<{ verified: boolean; type: 'phone' | 'email' }>(({ verified, type }) => {
  const IconComponent = verified ? CheckCircle : XCircle;
  const color = verified ? 'text-green-600' : 'text-red-600';
  const text = verified ? `${type === 'phone' ? 'Phone' : 'Email'} Verified` : `${type === 'phone' ? 'Phone' : 'Email'} Not Verified`;

  return (
    <div className={`flex items-center ${color}`}>
      <IconComponent className="w-4 h-4 mr-2" />
      <span className="text-sm font-medium">{text}</span>
    </div>
  );
});

VerificationBadge.displayName = 'VerificationBadge';

// Memoize the main ViewUserModal component
export const ViewUserModal = memo<ViewUserModalProps>(({ isOpen, user, onClose }) => {
  if (!isOpen || !user) return null;

  const formatDate = (timestamp: unknown) => {
    if (!timestamp) return 'N/A';
    
    // Handle Firestore Timestamp
    if (timestamp && typeof timestamp === 'object' && 'toDate' in timestamp && typeof timestamp.toDate === 'function') {
      return timestamp.toDate().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
    
    // Handle regular Date objects or date strings
    try {
      const date = new Date(timestamp as string | number | Date);
      if (isNaN(date.getTime())) return 'N/A';
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'N/A';
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const getGenderIcon = (gender: string) => {
    return gender === 'male' ? '♂' : '♀';
  };

  return (
    <div className="fixed inset-0 bg-opacity-10 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="glass-effect rounded-2xl border border-white/20 w-full max-w-4xl max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
              {user.profile.avatar ? (
                <Image 
                  src={user.profile.avatar} 
                  alt="Avatar" 
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full object-cover"
                />
              ) : (
                <span className="text-lg font-semibold text-yellow-800">
                  {getInitials(user.profile.firstName, user.profile.lastName)}
                </span>
              )}
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {user.profile.firstName} {user.profile.lastName}
              </h2>
              <p className="text-gray-600">User ID: {user.id}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors duration-300 p-2 rounded-full hover:bg-gray-100"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Profile Information */}
              <div className="glass-effect rounded-xl p-6 border border-white/10">
                <div className="flex items-center space-x-3 mb-4">
                  <User className="w-5 h-5 text-yellow-600" />
                  <h3 className="text-lg font-semibold text-gray-900">Profile Information</h3>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-500 w-20">Name:</span>
                    <span className="text-gray-900">
                      {user.profile.firstName} {user.profile.lastName}
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-500 w-20">Gender:</span>
                    <span className="text-gray-900 flex items-center">
                      {getGenderIcon(user.profile.gender)} {user.profile.gender}
                    </span>
                  </div>
                  {user.profile.birthDate && (
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-gray-500 w-20">Birth Date:</span>
                      <span className="text-gray-900">{user.profile.birthDate}</span>
                    </div>
                  )}
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-500 w-20">Language:</span>
                    <span className="text-gray-900 flex items-center">
                      <Globe className="w-4 h-4 mr-2" />
                      {user.profile.language.toUpperCase()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="glass-effect rounded-xl p-6 border border-white/10">
                <div className="flex items-center space-x-3 mb-4">
                  <Phone className="w-5 h-5 text-yellow-600" />
                  <h3 className="text-lg font-semibold text-gray-900">Contact Information</h3>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <Phone className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-900">{user.profile.phone}</span>
                  </div>
                  {user.profile.email && (
                    <div className="flex items-center space-x-3">
                      <Mail className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-900">{user.profile.email}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Authentication Status */}
              <div className="glass-effect rounded-xl p-6 border border-white/10">
                <div className="flex items-center space-x-3 mb-4">
                  <Shield className="w-5 h-5 text-yellow-600" />
                  <h3 className="text-lg font-semibold text-gray-900">Authentication</h3>
                </div>
                <div className="space-y-3">
                  <VerificationBadge verified={user.authentication.phoneVerified} type="phone" />
                  <VerificationBadge verified={user.authentication.emailVerified} type="email" />
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-500">Last Login:</span>
                    <span className="text-gray-900 flex items-center">
                      <Clock className="w-4 h-4 mr-2" />
                      {formatDate(user.authentication.lastLogin)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-500">Active Until:</span>
                    <span className="text-gray-900 flex items-center">
                      <Calendar className="w-4 h-4 mr-2" />
                      {formatDate(user.authentication.activeUntil)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              {/* Roles & Status */}
              <div className="glass-effect rounded-xl p-6 border border-white/10">
                <div className="flex items-center space-x-3 mb-4">
                  <Shield className="w-5 h-5 text-yellow-600" />
                  <h3 className="text-lg font-semibold text-gray-900">Roles & Status</h3>
                </div>
                <div className="space-y-4">
                  <div>
                    <span className="text-sm font-medium text-gray-500 block mb-2">Roles:</span>
                    <div className="flex flex-wrap gap-2">
                      {user.roles.map((role) => (
                        <RoleBadge key={role} role={role} />
                      ))}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500 block mb-2">Status:</span>
                    <StatusBadge status={user.status} />
                  </div>
                </div>
              </div>

              {/* User Statistics */}
              <div className="glass-effect rounded-xl p-6 border border-white/10">
                <div className="flex items-center space-x-3 mb-4">
                  <BarChart3 className="w-5 h-5 text-yellow-600" />
                  <h3 className="text-lg font-semibold text-gray-900">Statistics</h3>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Total Orders:</span>
                    <span className="text-gray-900 font-semibold">{user.stats.totalOrders}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Total Spent:</span>
                    <span className="text-gray-900 font-semibold">{user.stats.totalSpent} JOD</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Average Rating:</span>
                    <span className="text-gray-900 font-semibold">
                      {user.stats.averageRating.toFixed(1)} ⭐
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Joined:</span>
                    <span className="text-gray-900 flex items-center">
                      <Calendar className="w-4 h-4 mr-2" />
                      {formatDate(user.stats.joinedAt)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Settings */}
              <div className="glass-effect rounded-xl p-6 border border-white/10">
                <div className="flex items-center space-x-3 mb-4">
                  <Settings className="w-5 h-5 text-yellow-600" />
                  <h3 className="text-lg font-semibold text-gray-900">Settings</h3>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Theme:</span>
                    <span className="text-gray-900 capitalize">{user.settings.theme}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Order Updates:</span>
                    <span className={`text-sm font-medium ${user.settings.notifications.orderUpdates ? 'text-green-600' : 'text-red-600'}`}>
                      {user.settings.notifications.orderUpdates ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Promotions:</span>
                    <span className={`text-sm font-medium ${user.settings.notifications.promotions ? 'text-green-600' : 'text-red-600'}`}>
                      {user.settings.notifications.promotions ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">System:</span>
                    <span className={`text-sm font-medium ${user.settings.notifications.system ? 'text-green-600' : 'text-red-600'}`}>
                      {user.settings.notifications.system ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Share Location:</span>
                    <span className={`text-sm font-medium ${user.settings.privacy.shareLocation ? 'text-green-600' : 'text-red-600'}`}>
                      {user.settings.privacy.shareLocation ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Show Profile:</span>
                    <span className={`text-sm font-medium ${user.settings.privacy.showProfile ? 'text-green-600' : 'text-red-600'}`}>
                      {user.settings.privacy.showProfile ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Account Information */}
              <div className="glass-effect rounded-xl p-6 border border-white/10">
                <div className="flex items-center space-x-3 mb-4">
                  <Calendar className="w-5 h-5 text-yellow-600" />
                  <h3 className="text-lg font-semibold text-gray-900">Account Information</h3>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Created:</span>
                    <span className="text-gray-900 flex items-center">
                      <Calendar className="w-4 h-4 mr-2" />
                      {formatDate(user.profile.createdAt)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Last Updated:</span>
                    <span className="text-gray-900 flex items-center">
                      <Clock className="w-4 h-4 mr-2" />
                      {formatDate(user.profile.updatedAt)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end p-6 border-t border-white/10">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-all duration-300 font-medium"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
});

ViewUserModal.displayName = 'ViewUserModal';
