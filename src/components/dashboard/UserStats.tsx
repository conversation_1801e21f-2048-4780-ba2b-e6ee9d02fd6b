import React, { memo } from 'react';
import { Users, UserCheck, UserX, Shield, UserCog, Phone, Mail } from 'lucide-react';
import { UserStats as UserStatsType } from '@/services/UserService';

interface UserStatsProps {
  stats: UserStatsType | null;
  loading?: boolean;
}

// Memoize the StatCard component to prevent unnecessary re-renders
const StatCard = memo<{
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
  loading?: boolean;
}>(({ title, value, icon, color, loading }) => (
  <div className={`glass-effect rounded-2xl p-6 border border-white/20 ${color}`}>
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-600">{title}</p>
        {loading ? (
          <div className="animate-pulse bg-gray-200 h-8 w-16 rounded mt-2"></div>
        ) : (
          <p className="text-3xl font-bold text-gray-900 mt-2">{value.toLocaleString()}</p>
        )}
      </div>
      <div className="p-3 rounded-full bg-white/20">
        {icon}
      </div>
    </div>
  </div>
));

StatCard.displayName = 'StatCard';

// Memoize the main UserStats component
export const UserStats = memo<UserStatsProps>(({ stats, loading = false }) => {
  if (!stats && !loading) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">User Statistics</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Users"
            value={stats?.totalUsers || 0}
            icon={<Users className="w-6 h-6 text-blue-600" />}
            color="hover:shadow-lg transition-all duration-300"
            loading={loading}
          />
          <StatCard
            title="Active Users"
            value={stats?.activeUsers || 0}
            icon={<UserCheck className="w-6 h-6 text-green-600" />}
            color="hover:shadow-lg transition-all duration-300"
            loading={loading}
          />
          <StatCard
            title="Suspended Users"
            value={stats?.suspendedUsers || 0}
            icon={<UserX className="w-6 h-6 text-red-600" />}
            color="hover:shadow-lg transition-all duration-300"
            loading={loading}
          />
          <StatCard
            title="Verified Users"
            value={stats?.verifiedUsers || 0}
            icon={<Shield className="w-6 h-6 text-purple-600" />}
            color="hover:shadow-lg transition-all duration-300"
            loading={loading}
          />
        </div>
      </div>

      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Role Distribution</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <StatCard
            title="Customers"
            value={stats?.customers || 0}
            icon={<Users className="w-5 h-5 text-blue-500" />}
            color="bg-blue-50/50 hover:shadow-md transition-all duration-300"
            loading={loading}
          />
          <StatCard
            title="Drivers"
            value={stats?.drivers || 0}
            icon={<UserCog className="w-5 h-5 text-green-500" />}
            color="bg-green-50/50 hover:shadow-md transition-all duration-300"
            loading={loading}
          />
          <StatCard
            title="Admins"
            value={stats?.admins || 0}
            icon={<Shield className="w-5 h-5 text-purple-500" />}
            color="bg-purple-50/50 hover:shadow-md transition-all duration-300"
            loading={loading}
          />
          <StatCard
            title="Office Managers"
            value={stats?.officeManagers || 0}
            icon={<UserCog className="w-5 h-5 text-orange-500" />}
            color="bg-orange-50/50 hover:shadow-md transition-all duration-300"
            loading={loading}
          />
          <StatCard
            title="Support"
            value={stats?.supportUsers || 0}
            icon={<UserCheck className="w-5 h-5 text-teal-500" />}
            color="bg-teal-50/50 hover:shadow-md transition-all duration-300"
            loading={loading}
          />
        </div>
      </div>

      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Verification Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <StatCard
            title="Phone Verified"
            value={stats?.verifiedUsers || 0}
            icon={<Phone className="w-5 h-5 text-green-500" />}
            color="bg-green-50/50 hover:shadow-md transition-all duration-300"
            loading={loading}
          />
          <StatCard
            title="Email Verified"
            value={stats?.verifiedUsers || 0} // Note: This should be separate email verification count
            icon={<Mail className="w-5 h-5 text-blue-500" />}
            color="bg-blue-50/50 hover:shadow-md transition-all duration-300"
            loading={loading}
          />
        </div>
      </div>

      {stats && (
        <div className="glass-effect rounded-2xl p-6 border border-white/20">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Quick Insights</h3>
            <span className="text-sm text-gray-500">
              Last updated: {new Date().toLocaleDateString()}
            </span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <p className="text-gray-600">Active Rate</p>
              <p className="text-2xl font-bold text-green-600">
                {stats.totalUsers > 0 
                  ? ((stats.activeUsers / stats.totalUsers) * 100).toFixed(1)
                  : 0}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-gray-600">Verification Rate</p>
              <p className="text-2xl font-bold text-blue-600">
                {stats.totalUsers > 0 
                  ? ((stats.verifiedUsers / stats.totalUsers) * 100).toFixed(1)
                  : 0}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-gray-600">Customer Ratio</p>
              <p className="text-2xl font-bold text-purple-600">
                {stats.totalUsers > 0 
                  ? ((stats.customers / stats.totalUsers) * 100).toFixed(1)
                  : 0}%
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

UserStats.displayName = 'UserStats';
