import React, { useState, useCallback, memo } from 'react';
import { Search, Filter, X, Download, RefreshCw } from 'lucide-react';
import { UserRole } from '@/types/database';
import { UserFilters as UserFiltersType } from '@/services/UserService';

interface UserFiltersProps {
  filters: UserFiltersType;
  onFiltersChange: (filters: UserFiltersType) => void;
  onSearch: (searchTerm: string) => Promise<void>;
  onExport: () => Promise<void>;
  onRefresh: () => Promise<void>;
  loading?: boolean;
}

export const UserFilters = memo<UserFiltersProps>(({
  filters,
  onFiltersChange,
  onSearch,
  onExport,
  onRefresh,
  loading = false
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  const handleSearch = useCallback(async () => {
    await onSearch(searchTerm);
  }, [onSearch, searchTerm]);

  const handleSearchKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  }, [handleSearch]);

  const clearFilters = useCallback(() => {
    onFiltersChange({
      status: 'all',
      role: 'all',
      dateRange: 'all',
      verified: 'all'
    });
    setSearchTerm('');
  }, [onFiltersChange]);

  const hasActiveFilters = useCallback(() => {
    return filters.status !== 'all' || 
           filters.role !== 'all' || 
           filters.dateRange !== 'all' || 
           filters.verified !== 'all' ||
           searchTerm.trim() !== '';
  }, [filters, searchTerm]);

  const handleStatusChange = useCallback((value: string) => {
    onFiltersChange({...filters, status: value as UserFiltersType['status']});
  }, [filters, onFiltersChange]);

  const handleRoleChange = useCallback((value: string) => {
    onFiltersChange({...filters, role: value as UserFiltersType['role']});
  }, [filters, onFiltersChange]);

  const handleDateRangeChange = useCallback((value: string) => {
    onFiltersChange({...filters, dateRange: value as UserFiltersType['dateRange']});
  }, [filters, onFiltersChange]);

  const handleVerifiedChange = useCallback((value: string) => {
    onFiltersChange({...filters, verified: value as UserFiltersType['verified']});
  }, [filters, onFiltersChange]);

  const toggleAdvancedFilters = useCallback(() => {
    setShowAdvancedFilters(prev => !prev);
  }, []);

  return (
    <div className="glass-effect rounded-2xl p-6 border border-white/20 space-y-4">
      {/* Search Bar */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-4 top-3.5 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search users by name, email, or phone..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyPress={handleSearchKeyPress}
            className="w-full pl-12 pr-4 py-3 rounded-xl border-0 bg-white/50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-yellow-500 transition-all duration-300"
          />
        </div>
        <button
          onClick={handleSearch}
          disabled={loading}
          className="px-6 py-3 rounded-xl bg-yellow-500 text-black font-semibold hover:bg-yellow-600 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black mx-auto"></div>
          ) : (
            'Search'
          )}
        </button>
        <button
          onClick={toggleAdvancedFilters}
          className="px-6 py-3 rounded-xl bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all duration-300 flex items-center justify-center"
        >
          <Filter className="w-5 h-5 mr-2" />
          Filters
        </button>
      </div>

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
          {/* Status Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select
              value={filters.status || 'all'}
              onChange={(e) => handleStatusChange(e.target.value)}
              className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="suspended">Suspended</option>
            </select>
          </div>

          {/* Role Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
            <select
              value={filters.role || 'all'}
              onChange={(e) => handleRoleChange(e.target.value)}
              className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-500"
            >
              <option value="all">All Roles</option>
              <option value="customer">Customer</option>
              <option value="driver">Driver</option>
              <option value="admin">Admin</option>
              <option value="office_manager">Office Manager</option>
              <option value="support">Support</option>
            </select>
          </div>

          {/* Date Range Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
            <select
              value={filters.dateRange || 'all'}
              onChange={(e) => handleDateRangeChange(e.target.value)}
              className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-500"
            >
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="year">This Year</option>
            </select>
          </div>

          {/* Verification Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Verification</label>
            <select
              value={filters.verified || 'all'}
              onChange={(e) => handleVerifiedChange(e.target.value)}
              className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-500"
            >
              <option value="all">All Users</option>
              <option value="verified">Verified</option>
              <option value="unverified">Unverified</option>
            </select>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap items-center justify-between gap-4 pt-4 border-t border-gray-200">
        <div className="flex items-center gap-2">
          {hasActiveFilters() && (
            <button
              onClick={clearFilters}
              className="px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all duration-300 flex items-center"
            >
              <X className="w-4 h-4 mr-2" />
              Clear Filters
            </button>
          )}
          <span className="text-sm text-gray-500">
            {hasActiveFilters() ? 'Filters applied' : 'No filters applied'}
          </span>
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={onRefresh}
            disabled={loading}
            className="px-4 py-2 rounded-lg bg-blue-100 text-blue-700 hover:bg-blue-200 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button
            onClick={onExport}
            disabled={loading}
            className="px-4 py-2 rounded-lg bg-green-100 text-green-700 hover:bg-green-200 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </div>
    </div>
  );
});

UserFilters.displayName = 'UserFilters';
