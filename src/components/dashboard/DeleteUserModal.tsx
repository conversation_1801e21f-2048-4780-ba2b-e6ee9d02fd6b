import React, { memo } from 'react';
import { Alert<PERSON>riangle, Trash2, X } from 'lucide-react';
import { UserDocument } from '@/types/database';

interface DeleteUserModalProps {
  isOpen: boolean;
  user: UserDocument | null;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  loading?: boolean;
}

export const DeleteUserModal = memo<DeleteUserModalProps>(({
  isOpen,
  user,
  onClose,
  onConfirm,
  loading = false
}) => {
  if (!isOpen || !user) return null;

  const handleConfirm = async () => {
    try {
      await onConfirm();
      onClose();
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Delete failed:', error);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/20 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-100 rounded-full">
              <Trash2 className="w-6 h-6 text-red-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Delete User</h3>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors"
            disabled={loading}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Warning Icon */}
        <div className="flex justify-center mb-4">
          <div className="p-3 bg-red-100 rounded-full">
            <AlertTriangle className="w-12 h-12 text-red-600" />
          </div>
        </div>

        {/* Content */}
        <div className="text-center mb-6">
          <p className="text-gray-700 mb-3">
            Are you sure you want to permanently delete this user?
          </p>
          
          <div className="bg-gray-50 rounded-lg p-4 text-left">
            <p className="font-medium text-gray-900 mb-1">
              {user.profile.firstName} {user.profile.lastName}
            </p>
            <p className="text-sm text-gray-600">{user.profile.email}</p>
            <p className="text-sm text-gray-600">ID: {user.id}</p>
          </div>
          
          <p className="text-sm text-red-600 mt-3">
            <strong>Warning:</strong> This action will permanently remove the user from the system. This action cannot be undone.
          </p>
        </div>

        {/* Actions */}
        <div className="flex space-x-3">
          <button
            onClick={onClose}
            disabled={loading}
            className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            disabled={loading}
            className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Deleting...</span>
              </>
            ) : (
              <>
                <Trash2 className="w-4 h-4" />
                <span>Delete User</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
});

DeleteUserModal.displayName = 'DeleteUserModal';
