import React, { useState, memo, useEffect, useRef } from 'react';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  UserCheck, 
  UserX, 
  Shield, 
  Phone, 
  Mail,
  Eye
} from 'lucide-react';
import { UserDocument, UserRole } from '@/types/database';
import { RoleService } from '@/services/RoleService';

interface UserTableProps {
  users: UserDocument[];
  loading?: boolean;
  onEditUser: (user: UserDocument) => void;
  onViewUser: (user: UserDocument) => void;
  onUpdateStatus: (userId: string, status: UserDocument['status']) => Promise<void>;
  onUpdateRoles: (userId: string, roles: UserRole[]) => Promise<void>;
  onVerifyPhone: (userId: string) => Promise<void>;
  onVerifyEmail: (userId: string) => Promise<void>;
  onDeleteUser: (user: UserDocument) => void;
}

// Memoize the StatusBadge component
const StatusBadge = memo<{ status: UserDocument['status'] }>(({ status }) => {
  const getStatusConfig = (status: UserDocument['status']) => {
    switch (status) {
      case 'active':
        return { color: 'bg-green-100 text-green-800', text: 'Active' };
      case 'inactive':
        return { color: 'bg-gray-100 text-gray-800', text: 'Inactive' };
      case 'suspended':
        return { color: 'bg-red-100 text-red-800', text: 'Suspended' };
      default:
        return { color: 'bg-gray-100 text-gray-800', text: 'Unknown' };
    }
  };

  const config = getStatusConfig(status);

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.text}
    </span>
  );
});

StatusBadge.displayName = 'StatusBadge';

// Memoize the RoleBadge component
const RoleBadge = memo<{ role: UserRole }>(({ role }) => {
  const getRoleConfig = (role: UserRole) => {
    switch (role) {
      case 'customer':
        return { color: 'bg-blue-100 text-blue-800', text: 'Customer' };
      case 'driver':
        return { color: 'bg-green-100 text-green-800', text: 'Driver' };
      case 'admin':
        return { color: 'bg-purple-100 text-purple-800', text: 'Admin' };
      case 'office_manager':
        return { color: 'bg-orange-100 text-orange-800', text: 'Office Manager' };
      case 'support':
        return { color: 'bg-teal-100 text-teal-800', text: 'Support' };
      default:
        return { color: 'bg-gray-100 text-gray-800', text: 'Unknown' };
    }
  };

  const config = getRoleConfig(role);

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.text}
    </span>
  );
});

RoleBadge.displayName = 'RoleBadge';

// Memoize the VerificationStatus component
const VerificationStatus = memo<{ 
  phoneVerified: boolean; 
  emailVerified: boolean; 
  onVerifyPhone: () => void;
  onVerifyEmail: () => void;
}>(({ phoneVerified, emailVerified, onVerifyPhone, onVerifyEmail }) => (
  <div className="flex items-center gap-2">
    <button
      onClick={onVerifyPhone}
      className={`p-1 rounded-full ${phoneVerified ? 'text-green-600' : 'text-gray-400 hover:text-yellow-600'}`}
      title={phoneVerified ? 'Phone verified' : 'Verify phone'}
    >
      <Phone className="w-4 h-4" />
    </button>
    <button
      onClick={onVerifyEmail}
      className={`p-1 rounded-full ${emailVerified ? 'text-green-600' : 'text-gray-400 hover:text-yellow-600'}`}
      title={emailVerified ? 'Email verified' : 'Verify email'}
    >
      <Mail className="w-4 h-4" />
    </button>
  </div>
));

VerificationStatus.displayName = 'VerificationStatus';

// Memoize the main UserTable component
export const UserTable = memo<UserTableProps>(({
  users,
  loading = false,
  onEditUser,
  onViewUser,
  onUpdateStatus,
  onUpdateRoles,
  onVerifyPhone,
  onVerifyEmail,
  onDeleteUser
}) => {
  const [selectedUser, setSelectedUser] = useState<UserDocument | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const dropdownRefs = useRef<Map<string, HTMLDivElement>>(new Map());

  // Handle clicking outside the dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectedUser) {
        const dropdownRef = dropdownRefs.current.get(selectedUser.id);
        if (dropdownRef && !dropdownRef.contains(event.target as Node)) {
          setSelectedUser(null);
        }
      }
    };

    if (selectedUser) {
      // Use mousedown but with a delay to allow click events to process
      const timeoutId = setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside);
      }, 0);

      return () => {
        clearTimeout(timeoutId);
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [selectedUser]);

  const handleAction = async (action: () => Promise<void>, userId: string) => {
    setActionLoading(userId);
    try {
      await action();
    } finally {
      setActionLoading(null);
    }
  };

  const formatDate = (timestamp: unknown) => {
    if (!timestamp) return 'N/A';
    
    // Handle Firestore Timestamp
    if (timestamp && typeof timestamp === 'object' && 'toDate' in timestamp && typeof timestamp.toDate === 'function') {
      return timestamp.toDate().toLocaleDateString();
    }
    
    // Handle regular Date objects or date strings
    try {
      const date = new Date(timestamp as string | number | Date);
      if (isNaN(date.getTime())) return 'N/A';
      return date.toLocaleDateString();
    } catch {
      return 'N/A';
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  if (loading) {
    return (
      <div className="glass-effect rounded-2xl border border-white/20 overflow-hidden">
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading users...</p>
        </div>
      </div>
    );
  }

  if (users.length === 0) {
    return (
      <div className="glass-effect rounded-2xl border border-white/20 p-8 text-center">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <UserCheck className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
        <p className="text-gray-500">Try adjusting your search or filters to find users.</p>
      </div>
    );
  }

  return (
    <div className="glass-effect rounded-2xl border border-white/20 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50/50">
            <tr>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                User
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                Roles
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                Verification
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                Joined
              </th>
              <th className="px-6 py-4 text-right text-xs font-semibold text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {users.map((user) => (
              <tr key={user.id} className="hover:bg-gray-50/50 transition-all duration-300">
                {/* User Info */}
                <td className="px-6 py-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center">
                      {user.profile.avatar ? (
                        <img 
                          src={user.profile.avatar} 
                          alt="Avatar" 
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      ) : (
                        <span className="text-sm font-semibold text-yellow-800">
                          {getInitials(user.profile.firstName, user.profile.lastName)}
                        </span>
                      )}
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">
                        {user.profile.firstName} {user.profile.lastName}
                      </p>
                      <p className="text-sm text-gray-500">
                        {user.profile.email || user.profile.phone}
                      </p>
                      {user.profile.email && (
                        <p className="text-xs text-gray-400">{user.profile.phone}</p>
                      )}
                    </div>
                  </div>
                </td>

                {/* Roles */}
                <td className="px-6 py-4">
                  <div className="flex flex-wrap gap-1">
                    {user.roles.map((role) => (
                      <RoleBadge key={role} role={role} />
                    ))}
                  </div>
                </td>

                {/* Status */}
                <td className="px-6 py-4">
                  <StatusBadge status={user.status} />
                </td>

                {/* Verification */}
                <td className="px-6 py-4">
                  <VerificationStatus
                    phoneVerified={user.authentication.phoneVerified}
                    emailVerified={user.authentication.emailVerified}
                    onVerifyPhone={() => handleAction(() => onVerifyPhone(user.id), user.id)}
                    onVerifyEmail={() => handleAction(() => onVerifyEmail(user.id), user.id)}
                  />
                </td>

                {/* Joined Date */}
                <td className="px-6 py-4 text-sm text-gray-500">
                  {formatDate(user.profile.createdAt)}
                </td>

                {/* Actions */}
                <td className="px-6 py-4 text-right">
                  <div 
                    className="relative" 
                    ref={(el) => {
                      if (el) {
                        dropdownRefs.current.set(user.id, el);
                      } else {
                        dropdownRefs.current.delete(user.id);
                      }
                    }}
                  >
                    <button
                      onClick={() => setSelectedUser(selectedUser?.id === user.id ? null : user)}
                      className="text-gray-400 hover:text-gray-600 transition-colors duration-300 p-1 rounded-full hover:bg-gray-100"
                      disabled={actionLoading === user.id}
                    >
                      {actionLoading === user.id ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
                      ) : (
                        <MoreHorizontal className="w-5 h-5" />
                      )}
                    </button>

                    {/* Dropdown Menu */}
                    {selectedUser?.id === user.id && (
                      <div 
                        className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200"
                        onClick={(e) => e.stopPropagation()}
                        onMouseDown={(e) => e.stopPropagation()}
                      >
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onViewUser(user);
                            setSelectedUser(null);
                          }}
                          onMouseDown={(e) => e.stopPropagation()}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          <Eye className="w-4 h-4 mr-3" />
                          View Details
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onEditUser(user);
                            setSelectedUser(null);
                          }}
                          onMouseDown={(e) => e.stopPropagation()}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          <Edit className="w-4 h-4 mr-3" />
                          Edit User
                        </button>
                        <hr className="my-1" />
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAction(() => onUpdateStatus(user.id, 'active'), user.id);
                            setSelectedUser(null);
                          }}
                          onMouseDown={(e) => e.stopPropagation()}
                          disabled={user.status === 'active'}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <UserCheck className="w-4 h-4 mr-3" />
                          Activate
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAction(() => onUpdateStatus(user.id, 'suspended'), user.id);
                            setSelectedUser(null);
                          }}
                          onMouseDown={(e) => e.stopPropagation()}
                          disabled={user.status === 'suspended'}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <UserX className="w-4 h-4 mr-3" />
                          Suspend
                        </button>
                        <hr className="my-1" />
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onDeleteUser(user);
                            setSelectedUser(null);
                          }}
                          onMouseDown={(e) => e.stopPropagation()}
                          className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4 mr-3" />
                          Delete User
                        </button>
                      </div>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
});

UserTable.displayName = 'UserTable';
