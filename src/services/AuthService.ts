import { 
  signInWith<PERSON>hone<PERSON><PERSON>ber, 
  RecaptchaVerifier,
  signInWithPopup,
  GoogleAuthProvider,
  FacebookAuthProvider,
  OAuthProvider,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  sendPasswordResetEmail,
  update<PERSON>rofile,
  User,
  ConfirmationResult
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { UserDocument, UserRole, AuthProvider as AuthProviderType } from '@/types/database';

export interface AuthResult {
  user: User;
  isNewUser: boolean;
}

export interface AuthError extends Error {
  code: string;
}

// Phone Authentication Service
export class PhoneAuthService {
  private recaptchaVerifier: RecaptchaVerifier | null = null;

  // Cleanup method to dispose of reCAPTCHA verifier
  public cleanup(): void {
    if (this.recaptchaVerifier) {
      try {
        this.recaptchaVerifier.clear();
      } catch (error) {
        console.warn('Error clearing reCAPTCHA verifier:', error);
      }
      this.recaptchaVerifier = null;
    }
  }

  private async ensureRecaptcha(): Promise<RecaptchaVerifier> {
    if (this.recaptchaVerifier) {
      return this.recaptchaVerifier;
    }

    // Ensure we're in a browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      throw new Error('reCAPTCHA can only be initialized in a browser environment');
    }

    // Wait for both DOM and window to be fully loaded
    await this.waitForPageLoad();

    // Create invisible reCAPTCHA container if it doesn't exist
    const containerId = 'recaptcha-container-' + Date.now(); // Unique ID to avoid conflicts
    let recaptchaContainer = document.getElementById(containerId);
    if (!recaptchaContainer) {
      // Ensure document.body exists
      if (!document.body) {
        throw new Error('Document body not available for reCAPTCHA container creation');
      }

      recaptchaContainer = document.createElement('div');
      recaptchaContainer.id = containerId;
      // Use CSS classes instead of inline styles to avoid potential issues
      recaptchaContainer.className = 'recaptcha-invisible-container';
      recaptchaContainer.setAttribute('style', 'position: absolute !important; top: -9999px !important; left: -9999px !important; visibility: hidden !important; pointer-events: none !important; z-index: -1 !important;');
      document.body.appendChild(recaptchaContainer);
    }

    // Add a small delay to ensure the container is properly attached
    await new Promise(resolve => setTimeout(resolve, 100));

    try {
      // Create invisible reCAPTCHA verifier
      this.recaptchaVerifier = new RecaptchaVerifier(auth, containerId, {
        size: 'invisible',
        callback: () => {
          console.log('reCAPTCHA solved automatically');
        },
        'expired-callback': () => {
          console.log('reCAPTCHA expired, will recreate');
          this.cleanup();
        }
      });

      return this.recaptchaVerifier;
    } catch (error) {
      console.error('Failed to create RecaptchaVerifier:', error);
      // Clean up the container if verifier creation failed
      if (recaptchaContainer && recaptchaContainer.parentNode) {
        recaptchaContainer.parentNode.removeChild(recaptchaContainer);
      }
      throw new Error('Failed to initialize reCAPTCHA verification. Please refresh the page and try again.');
    }
  }

  private async waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve();
      } else {
        const handleLoad = () => {
          window.removeEventListener('load', handleLoad);
          document.removeEventListener('DOMContentLoaded', handleLoad);
          resolve();
        };
        
        window.addEventListener('load', handleLoad, { once: true });
        document.addEventListener('DOMContentLoaded', handleLoad, { once: true });
      }
    });
  }

  async sendOTP(phoneNumber: string): Promise<ConfirmationResult> {
    try {
      // Ensure phone number is in international format
      const formattedPhone = phoneNumber.startsWith('+') ? phoneNumber : `+962${phoneNumber}`;
      
      console.log('Initializing reCAPTCHA for phone authentication...');
      
      // Get or create invisible reCAPTCHA verifier - only when actually needed
      const recaptchaVerifier = await this.ensureRecaptcha();
      
      console.log('reCAPTCHA initialized, sending OTP...');
      
      const confirmationResult = await signInWithPhoneNumber(
        auth, 
        formattedPhone, 
        recaptchaVerifier
      );
      
      console.log('OTP sent successfully');
      return confirmationResult;
    } catch (error: any) {
      console.error('Send OTP error:', error);
      
      // Reset reCAPTCHA on error to allow retry
      this.cleanup();
      
      if (error.code === 'auth/too-many-requests') {
        throw new Error('Too many OTP requests. Please wait a few minutes before trying again.');
      } else if (error.code === 'auth/invalid-phone-number') {
        throw new Error('Invalid phone number format. Please enter a valid phone number.');
      } else if (error.code === 'auth/operation-not-allowed') {
        throw new Error('Phone authentication is not enabled. Please contact support.');
      } else if (error.code === 'auth/quota-exceeded') {
        throw new Error('SMS quota exceeded. Please try again later or contact support.');
      } else if (error.code === 'auth/network-request-failed') {
        throw new Error('Network error. Please check your internet connection and try again.');
      } else if (error.code === 'auth/recaptcha-not-enabled') {
        throw new Error('reCAPTCHA verification is required but not properly configured.');
      } else if (error.code === 'auth/maximum-second-factor-count-exceeded') {
        throw new Error('Maximum second factor count exceeded.');
      } else if (error.message?.includes('Document body not available') || error.message?.includes('browser environment')) {
        throw new Error('Authentication is not available. Please refresh the page and try again.');
      } else {
        throw new Error('Failed to send OTP. Please try again or contact support if the problem persists.');
      }
    }
  }
  
  async verifyOTP(confirmationResult: ConfirmationResult, code: string): Promise<AuthResult> {
    try {
      const result = await confirmationResult.confirm(code);
      const isNewUser = await this.createOrUpdateUser(result.user, 'phone');
      
      return {
        user: result.user,
        isNewUser
      };
    } catch (error: any) {
      console.error('Verify OTP error:', error);
      
      if (error.code === 'auth/invalid-verification-code') {
        throw new Error('Invalid verification code. Please check the 6-digit code and try again.');
      } else if (error.code === 'auth/code-expired') {
        throw new Error('Verification code has expired. Please request a new code.');
      } else if (error.code === 'auth/too-many-requests') {
        throw new Error('Too many verification attempts. Please wait before trying again.');
      } else if (error.code === 'auth/network-request-failed') {
        throw new Error('Network error. Please check your internet connection and try again.');
      } else {
        throw new Error('Failed to verify OTP. Please try again or request a new code.');
      }
    }
  }
  
  private async createOrUpdateUser(user: User, provider: AuthProviderType): Promise<boolean> {
    const userDocRef = doc(db, 'users', user.uid);
    const userDoc = await getDoc(userDocRef);
    
    if (!userDoc.exists()) {
      // Create new user
      const userData: Omit<UserDocument, 'id'> = {
        profile: {
          firstName: '',
          lastName: '',
          phone: user.phoneNumber || '',
          email: user.email || '',
          avatar: user.photoURL || '',
          gender: 'male', // Default, user can change later
          language: 'en',
          createdAt: serverTimestamp() as any,
          updatedAt: serverTimestamp() as any
        },
        roles: ['customer'],
        authentication: {
          phoneVerified: provider === 'phone',
          emailVerified: user.emailVerified,
          providers: [provider],
          lastLogin: serverTimestamp() as any,
          activeUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) as any // 30 days
        },
        settings: {
          notifications: {
            orderUpdates: true,
            promotions: true,
            system: true
          },
          privacy: {
            shareLocation: true,
            showProfile: true
          },
          theme: 'light'
        },
        stats: {
          totalOrders: 0,
          totalSpent: 0,
          averageRating: 5,
          joinedAt: serverTimestamp() as any
        },
        status: 'active'
      };
      
      await setDoc(userDocRef, userData);
      return true; // New user
    } else {
      // Update existing user
      await updateDoc(userDocRef, {
        'authentication.lastLogin': serverTimestamp(),
        'profile.updatedAt': serverTimestamp()
      });
      return false; // Existing user
    }
  }
}

// Social Authentication Service
export class SocialAuthService {
  async signInWithGoogle(): Promise<AuthResult> {
    try {
      const provider = new GoogleAuthProvider();
      provider.addScope('profile');
      provider.addScope('email');
      
      const result = await signInWithPopup(auth, provider);
      const isNewUser = await this.createOrUpdateUser(result.user, 'google');
      
      return {
        user: result.user,
        isNewUser
      };
    } catch (error: any) {
      console.error('Google sign in error:', error);
      
      if (error.code === 'auth/popup-closed-by-user') {
        throw new Error('Sign in was cancelled.');
      } else if (error.code === 'auth/popup-blocked') {
        throw new Error('Popup was blocked. Please allow popups and try again.');
      } else if (error.code === 'auth/operation-not-allowed') {
        throw new Error('Google sign-in is not enabled. Please contact support to enable this feature.');
      } else {
        throw new Error('Failed to sign in with Google.');
      }
    }
  }
  
  async signInWithFacebook(): Promise<AuthResult> {
    try {
      const provider = new FacebookAuthProvider();
      provider.addScope('email');
      
      const result = await signInWithPopup(auth, provider);
      const isNewUser = await this.createOrUpdateUser(result.user, 'facebook');
      
      return {
        user: result.user,
        isNewUser
      };
    } catch (error: any) {
      console.error('Facebook sign in error:', error);
      
      if (error.code === 'auth/popup-closed-by-user') {
        throw new Error('Sign in was cancelled.');
      } else if (error.code === 'auth/account-exists-with-different-credential') {
        throw new Error('An account with this email already exists with a different sign-in method.');
      } else if (error.code === 'auth/operation-not-allowed') {
        throw new Error('Facebook sign-in is not enabled. Please contact support to enable this feature.');
      } else {
        throw new Error('Failed to sign in with Facebook.');
      }
    }
  }
  
  async signInWithApple(): Promise<AuthResult> {
    try {
      const provider = new OAuthProvider('apple.com');
      provider.addScope('email');
      provider.addScope('name');
      
      const result = await signInWithPopup(auth, provider);
      const isNewUser = await this.createOrUpdateUser(result.user, 'apple');
      
      return {
        user: result.user,
        isNewUser
      };
    } catch (error: any) {
      console.error('Apple sign in error:', error);
      
      if (error.code === 'auth/popup-closed-by-user') {
        throw new Error('Sign in was cancelled.');
      } else if (error.code === 'auth/operation-not-allowed') {
        throw new Error('Apple sign-in is not enabled. Please contact support to enable this feature.');
      } else {
        throw new Error('Failed to sign in with Apple.');
      }
    }
  }
  
  private async createOrUpdateUser(user: User, provider: AuthProviderType): Promise<boolean> {
    const userDocRef = doc(db, 'users', user.uid);
    const userDoc = await getDoc(userDocRef);
    
    if (!userDoc.exists()) {
      const names = user.displayName?.split(' ') || ['', ''];
      const userData: Omit<UserDocument, 'id'> = {
        profile: {
          firstName: names[0] || '',
          lastName: names.slice(1).join(' ') || '',
          email: user.email || '',
          phone: user.phoneNumber || '',
          avatar: user.photoURL || '',
          gender: 'male', // Default, user can change later
          language: 'en',
          createdAt: serverTimestamp() as any,
          updatedAt: serverTimestamp() as any
        },
        roles: ['customer'],
        authentication: {
          phoneVerified: !!user.phoneNumber,
          emailVerified: user.emailVerified,
          providers: [provider],
          lastLogin: serverTimestamp() as any,
          activeUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) as any
        },
        settings: {
          notifications: {
            orderUpdates: true,
            promotions: true,
            system: true
          },
          privacy: {
            shareLocation: true,
            showProfile: true
          },
          theme: 'light'
        },
        stats: {
          totalOrders: 0,
          totalSpent: 0,
          averageRating: 5,
          joinedAt: serverTimestamp() as any
        },
        status: 'active'
      };
      
      await setDoc(userDocRef, userData);
      return true; // New user
    } else {
      // Update existing user login
      await updateDoc(userDocRef, {
        'authentication.lastLogin': serverTimestamp(),
        'profile.updatedAt': serverTimestamp()
      });
      return false; // Existing user
    }
  }
}

// Email/Password Authentication Service
export class EmailAuthService {
  async createAccount(email: string, password: string, firstName: string, lastName: string): Promise<AuthResult> {
    try {
      const result = await createUserWithEmailAndPassword(auth, email, password);
      
      // Update display name
      await updateProfile(result.user, {
        displayName: `${firstName} ${lastName}`.trim()
      });
      
      const isNewUser = await this.createOrUpdateUser(result.user, firstName, lastName);
      
      return {
        user: result.user,
        isNewUser
      };
    } catch (error: any) {
      console.error('Create account error:', error);
      
      if (error.code === 'auth/email-already-in-use') {
        throw new Error('An account with this email already exists.');
      } else if (error.code === 'auth/weak-password') {
        throw new Error('Password is too weak. Please choose a stronger password.');
      } else if (error.code === 'auth/invalid-email') {
        throw new Error('Invalid email address.');
      } else if (error.code === 'auth/operation-not-allowed') {
        throw new Error('Email/password authentication is not enabled. Please contact support to enable this feature.');
      } else {
        throw new Error('Failed to create account. Please try again.');
      }
    }
  }
  
  async signIn(email: string, password: string): Promise<AuthResult> {
    try {
      const result = await signInWithEmailAndPassword(auth, email, password);
      await this.updateLastLogin(result.user);
      
      return {
        user: result.user,
        isNewUser: false
      };
    } catch (error: any) {
      console.error('Sign in error:', error);
      
      if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
        throw new Error('Invalid email or password.');
      } else if (error.code === 'auth/invalid-email') {
        throw new Error('Invalid email address.');
      } else if (error.code === 'auth/too-many-requests') {
        throw new Error('Too many failed attempts. Please try again later.');
      } else if (error.code === 'auth/operation-not-allowed') {
        throw new Error('Email/password authentication is not enabled. Please contact support to enable this feature.');
      } else {
        throw new Error('Failed to sign in. Please try again.');
      }
    }
  }
  
  async resetPassword(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error: any) {
      console.error('Password reset error:', error);
      
      if (error.code === 'auth/user-not-found') {
        throw new Error('No account found with this email address.');
      } else if (error.code === 'auth/invalid-email') {
        throw new Error('Invalid email address.');
      } else {
        throw new Error('Failed to send password reset email.');
      }
    }
  }
  
  private async createOrUpdateUser(user: User, firstName: string, lastName: string): Promise<boolean> {
    const userDocRef = doc(db, 'users', user.uid);
    const userDoc = await getDoc(userDocRef);
    
    if (!userDoc.exists()) {
      const userData: Omit<UserDocument, 'id'> = {
        profile: {
          firstName,
          lastName,
          email: user.email || '',
          phone: user.phoneNumber || '',
          avatar: user.photoURL || '',
          gender: 'male', // Default, user can change later
          language: 'en',
          createdAt: serverTimestamp() as any,
          updatedAt: serverTimestamp() as any
        },
        roles: ['customer'],
        authentication: {
          phoneVerified: !!user.phoneNumber,
          emailVerified: user.emailVerified,
          providers: ['phone'],
          lastLogin: serverTimestamp() as any,
          activeUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) as any
        },
        settings: {
          notifications: {
            orderUpdates: true,
            promotions: true,
            system: true
          },
          privacy: {
            shareLocation: true,
            showProfile: true
          },
          theme: 'light'
        },
        stats: {
          totalOrders: 0,
          totalSpent: 0,
          averageRating: 5,
          joinedAt: serverTimestamp() as any
        },
        status: 'active'
      };
      
      await setDoc(userDocRef, userData);
      return true; // New user
    }
    
    return false; // Existing user
  }
  
  private async updateLastLogin(user: User): Promise<void> {
    const userDocRef = doc(db, 'users', user.uid);
    await updateDoc(userDocRef, {
      'authentication.lastLogin': serverTimestamp(),
      'profile.updatedAt': serverTimestamp()
    });
  }
}

// Singleton instances
export const phoneAuthService = new PhoneAuthService();
export const socialAuthService = new SocialAuthService();
export const emailAuthService = new EmailAuthService();

// Main Auth Service
export class AuthService {
  static phone = phoneAuthService;
  static social = socialAuthService;
  static email = emailAuthService;
  
  static async signOut(): Promise<void> {
    try {
      await auth.signOut();
    } catch (error: any) {
      console.error('Sign out error:', error);
      throw new Error('Failed to sign out.');
    }
  }
  
  static getCurrentUser(): User | null {
    return auth.currentUser;
  }
  
  static async updateUserRole(userId: string, roles: UserRole[]): Promise<void> {
    try {
      const userDocRef = doc(db, 'users', userId);
      await updateDoc(userDocRef, {
        roles,
        'profile.updatedAt': serverTimestamp()
      });
    } catch (error: any) {
      console.error('Update user role error:', error);
      throw new Error('Failed to update user roles.');
    }
  }
  
  static async updateUserProfile(userId: string, profileData: Partial<UserDocument['profile']>): Promise<void> {
    try {
      const userDocRef = doc(db, 'users', userId);
      const updateData: any = {
        'profile.updatedAt': serverTimestamp()
      };
      
      // Add profile fields with proper nesting
      Object.entries(profileData).forEach(([key, value]) => {
        updateData[`profile.${key}`] = value;
      });
      
      await updateDoc(userDocRef, updateData);
    } catch (error: any) {
      console.error('Update user profile error:', error);
      throw new Error('Failed to update user profile.');
    }
  }
}
