import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter,
  QueryDocumentSnapshot,
  DocumentData,
  Timestamp,
  GeoPoint
} from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { DriverDocument, ContactInfo, InsuranceInfo, WorkingHours, GeoArea } from '@/types/database'

export interface DriverFilters {
  search?: string
  status?: 'all' | 'online' | 'offline' | 'available' | 'busy'
  verification?: 'all' | 'pending' | 'approved' | 'rejected'
  location?: string
  dateRange?: 'today' | 'week' | 'month' | 'all'
  rating?: number
}

export interface DriverStats {
  total: number
  online: number
  offline: number
  available: number
  busy: number
  pending: number
  approved: number
  rejected: number
  averageRating: number
  totalEarnings: number
}

export interface CreateDriverData {
  personalInfo: {
    firstName: string
    lastName: string
    phone: string
    email: string
    nationalId: string
    licenseNumber: string
    licenseExpiry: string
    emergencyContact: ContactInfo
  }
  vehicle: {
    make: string
    model: string
    year: number
    color: string
    plateNumber: string
    licenseNumber: string
    licenseExpiry: string
    insurance: InsuranceInfo
  }
  documents?: {
    nationalIdFront?: string
    nationalIdBack?: string
    drivingLicenseFront?: string
    drivingLicenseBack?: string
    vehicleLicense?: string
    vehicleImages?: string[]
    insuranceCertificate?: string
    backgroundCheck?: string
  }
  schedule?: {
    workingHours?: WorkingHours
    preferredAreas?: GeoArea[]
  }
}

export interface UpdateDriverData {
  personalInfo?: Partial<CreateDriverData['personalInfo']>
  vehicle?: Partial<CreateDriverData['vehicle']>
  documents?: Partial<CreateDriverData['documents']>
  verification?: {
    status?: 'pending' | 'approved' | 'rejected'
    notes?: string
  }
  status?: {
    online?: boolean
    available?: boolean
  }
  location?: {
    current?: GeoPoint
    heading?: number
    speed?: number
    accuracy?: number
  }
  schedule?: Partial<CreateDriverData['schedule']>
}

export interface GetDriversResult {
  drivers: DriverDocument[]
  lastDoc?: QueryDocumentSnapshot<DocumentData>
  hasMore: boolean
}

export class DriverService {
  private static readonly COLLECTION = 'drivers'

  // Create a new driver
  static async createDriver(driverData: CreateDriverData): Promise<string> {
    try {
      const now = Timestamp.now()
      
      const driverDoc: Omit<DriverDocument, 'id'> = {
        personalInfo: driverData.personalInfo,
        vehicle: driverData.vehicle,
        documents: {
          nationalIdFront: driverData.documents?.nationalIdFront || '',
          nationalIdBack: driverData.documents?.nationalIdBack || '',
          drivingLicenseFront: driverData.documents?.drivingLicenseFront || '',
          drivingLicenseBack: driverData.documents?.drivingLicenseBack || '',
          vehicleLicense: driverData.documents?.vehicleLicense || '',
          vehicleImages: driverData.documents?.vehicleImages || [],
          insuranceCertificate: driverData.documents?.insuranceCertificate || '',
          backgroundCheck: driverData.documents?.backgroundCheck || ''
        },
        verification: {
          status: 'pending'
          // verifiedAt, verifiedBy, and notes are optional and will be added when verification is completed
        },
        location: {
          current: new GeoPoint(31.9539, 35.9106), // Default to Amman, Jordan
          heading: 0,
          speed: 0,
          accuracy: 0,
          lastUpdated: now
        },
        status: {
          online: false,
          available: false,
          lastSeen: now
          // currentOrder is optional and will be set when driver accepts an order
        },
        earnings: {
          totalEarnings: 0,
          pendingPayout: 0,
          completedTrips: 0,
          rating: {
            average: 5.0,
            count: 0,
            breakdown: {
              5: 0,
              4: 0,
              3: 0,
              2: 0,
              1: 0
            }
          }
        },
        schedule: {
          workingHours: driverData.schedule?.workingHours || {
            monday: { start: '08:00', end: '18:00', active: true },
            tuesday: { start: '08:00', end: '18:00', active: true },
            wednesday: { start: '08:00', end: '18:00', active: true },
            thursday: { start: '08:00', end: '18:00', active: true },
            friday: { start: '08:00', end: '18:00', active: true },
            saturday: { start: '08:00', end: '18:00', active: true },
            sunday: { start: '08:00', end: '18:00', active: false }
          },
          preferredAreas: driverData.schedule?.preferredAreas || []
        }
      }

      const docRef = await addDoc(collection(db, this.COLLECTION), driverDoc)
      return docRef.id
    } catch (error) {
      console.error('Error creating driver:', error)
      throw new Error('Failed to create driver')
    }
  }

  // Get drivers with filtering and pagination
  static async getDrivers(
    filters: DriverFilters = {},
    lastDoc?: QueryDocumentSnapshot<DocumentData>,
    limitCount: number = 50
  ): Promise<GetDriversResult> {
    try {
      let q = collection(db, this.COLLECTION)
      const constraints = []

      // Apply filters
      if (filters.verification && filters.verification !== 'all') {
        constraints.push(where('verification.status', '==', filters.verification))
      }

      if (filters.status && filters.status !== 'all') {
        switch (filters.status) {
          case 'online':
            constraints.push(where('status.online', '==', true))
            break
          case 'offline':
            constraints.push(where('status.online', '==', false))
            break
          case 'available':
            constraints.push(where('status.available', '==', true))
            break
          case 'busy':
            constraints.push(where('status.available', '==', false))
            break
        }
      }

      if (filters.rating) {
        constraints.push(where('earnings.rating.average', '>=', filters.rating))
      }

      // Add ordering and pagination
      constraints.push(orderBy('personalInfo.firstName', 'asc'))
      constraints.push(limit(limitCount))

      if (lastDoc) {
        constraints.push(startAfter(lastDoc))
      }

      const querySnapshot = await getDocs(query(q, ...constraints))
      
      const drivers: DriverDocument[] = []
      querySnapshot.forEach((doc) => {
        drivers.push({ id: doc.id, ...doc.data() } as DriverDocument)
      })

      // Apply search filter (client-side for now)
      let filteredDrivers = drivers
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase()
        filteredDrivers = drivers.filter(driver => 
          driver.personalInfo.firstName.toLowerCase().includes(searchTerm) ||
          driver.personalInfo.lastName.toLowerCase().includes(searchTerm) ||
          driver.personalInfo.phone.includes(searchTerm) ||
          driver.personalInfo.email.toLowerCase().includes(searchTerm) ||
          driver.personalInfo.licenseNumber.toLowerCase().includes(searchTerm) ||
          driver.vehicle.plateNumber.toLowerCase().includes(searchTerm)
        )
      }

      const lastDocument = querySnapshot.docs[querySnapshot.docs.length - 1]
      const hasMore = querySnapshot.docs.length === limitCount

      return {
        drivers: filteredDrivers,
        lastDoc: lastDocument,
        hasMore
      }
    } catch (error) {
      console.error('Error getting drivers:', error)
      throw new Error('Failed to get drivers')
    }
  }

  // Get driver by ID
  static async getDriverById(id: string): Promise<DriverDocument | null> {
    try {
      const docRef = doc(db, this.COLLECTION, id)
      const docSnap = await getDoc(docRef)
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as DriverDocument
      }
      
      return null
    } catch (error) {
      console.error('Error getting driver:', error)
      throw new Error('Failed to get driver')
    }
  }

  // Update driver
  static async updateDriver(id: string, data: UpdateDriverData): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION, id)
      const updateData: any = {}

      if (data.personalInfo) {
        Object.keys(data.personalInfo).forEach(key => {
          updateData[`personalInfo.${key}`] = data.personalInfo![key as keyof typeof data.personalInfo]
        })
      }

      if (data.vehicle) {
        Object.keys(data.vehicle).forEach(key => {
          updateData[`vehicle.${key}`] = data.vehicle![key as keyof typeof data.vehicle]
        })
      }

      if (data.documents) {
        Object.keys(data.documents).forEach(key => {
          updateData[`documents.${key}`] = data.documents![key as keyof typeof data.documents]
        })
      }

      if (data.verification) {
        Object.keys(data.verification).forEach(key => {
          updateData[`verification.${key}`] = data.verification![key as keyof typeof data.verification]
        })
        if (data.verification.status) {
          updateData['verification.verifiedAt'] = Timestamp.now()
        }
      }

      if (data.status) {
        Object.keys(data.status).forEach(key => {
          updateData[`status.${key}`] = data.status![key as keyof typeof data.status]
        })
        updateData['status.lastSeen'] = Timestamp.now()
      }

      if (data.location) {
        Object.keys(data.location).forEach(key => {
          updateData[`location.${key}`] = data.location![key as keyof typeof data.location]
        })
        updateData['location.lastUpdated'] = Timestamp.now()
      }

      if (data.schedule) {
        Object.keys(data.schedule).forEach(key => {
          updateData[`schedule.${key}`] = data.schedule![key as keyof typeof data.schedule]
        })
      }

      await updateDoc(docRef, updateData)
    } catch (error) {
      console.error('Error updating driver:', error)
      throw new Error('Failed to update driver')
    }
  }

  // Delete driver
  static async deleteDriver(id: string): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION, id)
      await deleteDoc(docRef)
    } catch (error) {
      console.error('Error deleting driver:', error)
      throw new Error('Failed to delete driver')
    }
  }

  // Verify driver
  static async verifyDriver(id: string, approved: boolean, notes?: string, verifiedBy?: string): Promise<void> {
    try {
      const updateData: UpdateDriverData = {
        verification: {
          status: approved ? 'approved' : 'rejected',
          notes: notes || ''
        }
      }

      if (verifiedBy) {
        (updateData.verification as any).verifiedBy = verifiedBy
      }

      await this.updateDriver(id, updateData)
    } catch (error) {
      console.error('Error verifying driver:', error)
      throw new Error('Failed to verify driver')
    }
  }

  // Update driver status (online/offline, available/busy)
  static async updateDriverStatus(id: string, online: boolean, available: boolean): Promise<void> {
    try {
      await this.updateDriver(id, {
        status: {
          online,
          available
        }
      })
    } catch (error) {
      console.error('Error updating driver status:', error)
      throw new Error('Failed to update driver status')
    }
  }

  // Update driver location
  static async updateDriverLocation(
    id: string,
    coordinates: GeoPoint,
    heading: number = 0,
    speed: number = 0,
    accuracy: number = 0
  ): Promise<void> {
    try {
      await this.updateDriver(id, {
        location: {
          current: coordinates,
          heading,
          speed,
          accuracy
        }
      })
    } catch (error) {
      console.error('Error updating driver location:', error)
      throw new Error('Failed to update driver location')
    }
  }

  // Get driver statistics
  static async getDriverStats(): Promise<DriverStats> {
    try {
      const querySnapshot = await getDocs(collection(db, this.COLLECTION))

      let total = 0
      let online = 0
      let offline = 0
      let available = 0
      let busy = 0
      let pending = 0
      let approved = 0
      let rejected = 0
      let totalRating = 0
      let ratingCount = 0
      let totalEarnings = 0

      querySnapshot.forEach((doc) => {
        const driver = doc.data() as DriverDocument
        total++

        // Status counts
        if (driver.status.online) {
          online++
        } else {
          offline++
        }

        if (driver.status.available) {
          available++
        } else {
          busy++
        }

        // Verification counts
        switch (driver.verification.status) {
          case 'pending':
            pending++
            break
          case 'approved':
            approved++
            break
          case 'rejected':
            rejected++
            break
        }

        // Rating calculation
        if (driver.earnings.rating.count > 0) {
          totalRating += driver.earnings.rating.average * driver.earnings.rating.count
          ratingCount += driver.earnings.rating.count
        }

        // Earnings calculation
        totalEarnings += driver.earnings.totalEarnings
      })

      const averageRating = ratingCount > 0 ? totalRating / ratingCount : 0

      return {
        total,
        online,
        offline,
        available,
        busy,
        pending,
        approved,
        rejected,
        averageRating: Math.round(averageRating * 10) / 10,
        totalEarnings: Math.round(totalEarnings * 100) / 100
      }
    } catch (error) {
      console.error('Error getting driver stats:', error)
      throw new Error('Failed to get driver stats')
    }
  }

  // Get online and available drivers
  static async getOnlineDrivers(): Promise<DriverDocument[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION),
        where('status.online', '==', true),
        where('status.available', '==', true),
        where('verification.status', '==', 'approved')
      )

      const querySnapshot = await getDocs(q)
      const drivers: DriverDocument[] = []

      querySnapshot.forEach((doc) => {
        drivers.push({ id: doc.id, ...doc.data() } as DriverDocument)
      })

      return drivers
    } catch (error) {
      console.error('Error getting online drivers:', error)
      throw new Error('Failed to get online drivers')
    }
  }

  // Get pending verification drivers
  static async getPendingDrivers(): Promise<DriverDocument[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION),
        where('verification.status', '==', 'pending'),
        orderBy('personalInfo.firstName', 'asc')
      )

      const querySnapshot = await getDocs(q)
      const drivers: DriverDocument[] = []

      querySnapshot.forEach((doc) => {
        drivers.push({ id: doc.id, ...doc.data() } as DriverDocument)
      })

      return drivers
    } catch (error) {
      console.error('Error getting pending drivers:', error)
      throw new Error('Failed to get pending drivers')
    }
  }

  // Get users with driver role and their corresponding driver documents
  static async getDriverUsers(): Promise<Array<{
    user: any;
    driverDoc: DriverDocument | null;
    hasDriverInfo: boolean;
  }>> {
    try {
      // Get all users with driver role
      const usersQuery = query(
        collection(db, 'users'),
        where('roles', 'array-contains', 'driver'),
        orderBy('profile.firstName', 'asc')
      )

      const usersSnapshot = await getDocs(usersQuery)
      const results = []

      for (const userDoc of usersSnapshot.docs) {
        const userData = { id: userDoc.id, ...userDoc.data() }

        // Check if this user has a corresponding driver document
        const driverDocRef = doc(db, this.COLLECTION, userDoc.id)
        const driverDocSnap = await getDoc(driverDocRef)

        const driverDoc = driverDocSnap.exists()
          ? { id: driverDocSnap.id, ...driverDocSnap.data() } as DriverDocument
          : null

        results.push({
          user: userData,
          driverDoc,
          hasDriverInfo: driverDocSnap.exists()
        })
      }

      return results
    } catch (error) {
      console.error('Error getting driver users:', error)
      throw new Error('Failed to get driver users')
    }
  }

  // Create or update driver document for existing user
  static async createOrUpdateDriverForUser(userId: string, driverData: CreateDriverData): Promise<void> {
    try {
      const now = Timestamp.now()

      const driverDoc: Omit<DriverDocument, 'id'> = {
        personalInfo: driverData.personalInfo,
        vehicle: driverData.vehicle,
        documents: {
          nationalIdFront: driverData.documents?.nationalIdFront || '',
          nationalIdBack: driverData.documents?.nationalIdBack || '',
          drivingLicenseFront: driverData.documents?.drivingLicenseFront || '',
          drivingLicenseBack: driverData.documents?.drivingLicenseBack || '',
          vehicleLicense: driverData.documents?.vehicleLicense || '',
          vehicleImages: driverData.documents?.vehicleImages || [],
          insuranceCertificate: driverData.documents?.insuranceCertificate || '',
          backgroundCheck: driverData.documents?.backgroundCheck || ''
        },
        verification: {
          status: 'pending'
          // verifiedAt, verifiedBy, and notes are optional and will be added when verification is completed
        },
        location: {
          current: new GeoPoint(31.9539, 35.9106), // Default to Amman, Jordan
          heading: 0,
          speed: 0,
          accuracy: 0,
          lastUpdated: now
        },
        status: {
          online: false,
          available: false,
          lastSeen: now
          // currentOrder is optional and will be set when driver accepts an order
        },
        earnings: {
          totalEarnings: 0,
          pendingPayout: 0,
          completedTrips: 0,
          rating: {
            average: 5.0,
            count: 0,
            breakdown: {
              5: 0,
              4: 0,
              3: 0,
              2: 0,
              1: 0
            }
          }
        },
        schedule: {
          workingHours: driverData.schedule?.workingHours || {
            monday: { start: '08:00', end: '18:00', active: true },
            tuesday: { start: '08:00', end: '18:00', active: true },
            wednesday: { start: '08:00', end: '18:00', active: true },
            thursday: { start: '08:00', end: '18:00', active: true },
            friday: { start: '08:00', end: '18:00', active: true },
            saturday: { start: '08:00', end: '18:00', active: true },
            sunday: { start: '08:00', end: '18:00', active: false }
          },
          preferredAreas: driverData.schedule?.preferredAreas || []
        }
      }

      // Use the user ID as the driver document ID to maintain the relationship
      await updateDoc(doc(db, this.COLLECTION, userId), driverDoc)
    } catch (error) {
      console.error('Error creating/updating driver for user:', error)
      throw new Error('Failed to create/update driver information')
    }
  }

  // Export drivers data
  static async exportDrivers(filters: DriverFilters = {}): Promise<DriverDocument[]> {
    try {
      const result = await this.getDrivers(filters, undefined, 1000) // Get up to 1000 drivers for export
      return result.drivers
    } catch (error) {
      console.error('Error exporting drivers:', error)
      throw new Error('Failed to export drivers')
    }
  }

  // Search drivers
  static async searchDrivers(searchTerm: string, limitCount: number = 20): Promise<DriverDocument[]> {
    try {
      const result = await this.getDrivers({ search: searchTerm }, undefined, limitCount)
      return result.drivers
    } catch (error) {
      console.error('Error searching drivers:', error)
      throw new Error('Failed to search drivers')
    }
  }
}
