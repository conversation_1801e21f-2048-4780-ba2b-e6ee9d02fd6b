import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter, 
  QueryDocumentSnapshot,
  DocumentData,
  serverTimestamp,
  writeBatch,
  Timestamp,
  setDoc
} from 'firebase/firestore';
import { 
  createUserWithEmailAndPassword,
  updateProfile,
  UserCredential
} from 'firebase/auth';
import { db, auth } from '@/lib/firebase';
import { UserDocument, UserRole, AuthProvider } from '@/types/database';
import { RoleService } from './RoleService';

export interface UserFilters {
  status?: 'active' | 'inactive' | 'suspended' | 'all';
  role?: UserRole | 'all';
  search?: string;
  dateRange?: 'today' | 'week' | 'month' | 'year' | 'all';
  verified?: 'verified' | 'unverified' | 'all';
}

export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  suspendedUsers: number;
  customers: number;
  drivers: number;
  admins: number;
  officeManagers: number;
  supportUsers: number;
  verifiedUsers: number;
  unverifiedUsers: number;
}

export interface CreateUserData {
  profile: {
    firstName: string;
    lastName: string;
    phone: string;
    email?: string;
    gender: 'male' | 'female';
    birthDate?: string;
    language: 'en' | 'ar';
  };
  roles: UserRole[];
  authentication: {
    phoneVerified: boolean;
    emailVerified: boolean;
    providers: AuthProvider[];
    password?: string; // Added for email authentication
  };
}

export interface UpdateUserData {
  profile?: Partial<UserDocument['profile']>;
  roles?: UserRole[];
  status?: UserDocument['status'];
  authentication?: Partial<UserDocument['authentication']>;
  settings?: Partial<UserDocument['settings']>;
}

export class UserService {
  private static readonly COLLECTION = 'users';
  private static readonly BATCH_SIZE = 50;

  /**
   * Get a single user by ID
   */
  static async getUserById(userId: string): Promise<UserDocument | null> {
    try {
      const userDoc = await getDoc(doc(db, this.COLLECTION, userId));
      
      if (!userDoc.exists()) {
        return null;
      }

      return {
        id: userDoc.id,
        ...userDoc.data()
      } as UserDocument;
    } catch (error) {
      console.error('Error getting user by ID:', error);
      throw new Error('Failed to get user');
    }
  }

  /**
   * Get users with pagination and filters
   */
  static async getUsers(
    filters: UserFilters = {},
    lastDoc?: QueryDocumentSnapshot<DocumentData>,
    pageSize: number = this.BATCH_SIZE
  ): Promise<{ users: UserDocument[]; lastDoc?: QueryDocumentSnapshot<DocumentData>; hasMore: boolean }> {
    try {
      let q = collection(db, this.COLLECTION);
      const constraints: any[] = [];

      // Apply filters
      if (filters.status && filters.status !== 'all') {
        constraints.push(where('status', '==', filters.status));
      }

      if (filters.role && filters.role !== 'all') {
        constraints.push(where('roles', 'array-contains', filters.role));
      }

      if (filters.dateRange && filters.dateRange !== 'all') {
        const dateFilter = this.getDateFilter(filters.dateRange);
        if (dateFilter) {
          constraints.push(where('profile.createdAt', '>=', dateFilter));
        }
      }

      if (filters.verified && filters.verified !== 'all') {
        const verifiedField = filters.verified === 'verified' ? 'authentication.phoneVerified' : 'authentication.phoneVerified';
        constraints.push(where(verifiedField, '==', filters.verified === 'verified'));
      }

      // Add ordering
      constraints.push(orderBy('profile.createdAt', 'desc'));
      constraints.push(limit(pageSize));

      // Add pagination
      if (lastDoc) {
        constraints.push(startAfter(lastDoc));
      }

      // Apply constraints
      if (constraints.length > 0) {
        q = query(q, ...constraints);
      }

      const snapshot = await getDocs(q);
      const users: UserDocument[] = [];

      snapshot.forEach((doc) => {
        users.push({
          id: doc.id,
          ...doc.data()
        } as UserDocument);
      });

      const lastVisible = snapshot.docs[snapshot.docs.length - 1];
      const hasMore = snapshot.docs.length === pageSize;

      return {
        users,
        lastDoc: lastVisible,
        hasMore
      };
    } catch (error) {
      console.error('Error getting users:', error);
      throw new Error('Failed to get users');
    }
  }

  /**
   * Search users by name, email, or phone
   */
  static async searchUsers(searchTerm: string, limit: number = 20): Promise<UserDocument[]> {
    try {
      // Note: Firestore doesn't support full-text search, so we'll implement a basic search
      // In production, consider using Algolia or similar service for better search
      const users = await this.getUsers({ search: searchTerm }, undefined, limit);
      return users.users.filter(user => 
        user.profile.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.profile.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.profile.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.profile.phone.includes(searchTerm)
      );
    } catch (error) {
      console.error('Error searching users:', error);
      throw new Error('Failed to search users');
    }
  }

  /**
   * Create a new user
   */
  static async createUser(userData: CreateUserData): Promise<string> {
    try {
      // Validate user data
      this.validateUserData(userData);

      let userCredential: UserCredential | null = null;
      let authUserId: string;

      // Create user in Firebase Authentication first
      if (userData.profile.email && userData.authentication.password) {
        // Create user with email and password
        userCredential = await createUserWithEmailAndPassword(
          auth,
          userData.profile.email,
          userData.authentication.password
        );
        authUserId = userCredential.user.uid;
        
        // Update the user's display name
        await updateProfile(userCredential.user, {
          displayName: `${userData.profile.firstName} ${userData.profile.lastName}`
        });
      } else {
        // For phone-only users, we'll create a custom UID
        // In a real app, you might want to implement phone authentication differently
        authUserId = `phone_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      }

      // Prepare user document for Firestore
      const userDoc = {
        profile: {
          ...userData.profile,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        },
        roles: userData.roles,
        authentication: {
          ...userData.authentication,
          uid: authUserId, // Link to Firebase Auth UID
          lastLogin: serverTimestamp(),
          activeUntil: serverTimestamp(),
        },
        settings: {
          notifications: {
            orderUpdates: true,
            promotions: true,
            system: true,
          },
          privacy: {
            shareLocation: true,
            showProfile: true,
          },
          theme: 'light',
        },
        stats: {
          totalOrders: 0,
          totalSpent: 0,
          averageRating: 5,
          joinedAt: serverTimestamp(),
        },
        status: 'active' as const,
      };

      // Store user data in Firestore using the Auth UID as document ID
      await setDoc(doc(db, this.COLLECTION, authUserId), userDoc);
      
      return authUserId;
    } catch (error) {
      console.error('Error creating user:', error);
      throw new Error('Failed to create user');
    }
  }

  /**
   * Update an existing user
   */
  static async updateUser(userId: string, updateData: UpdateUserData): Promise<void> {
    try {
      const userRef = doc(db, this.COLLECTION, userId);
      
      // Prepare update data
      const updateFields: any = {
        'profile.updatedAt': serverTimestamp(),
      };

      if (updateData.profile) {
        Object.keys(updateData.profile).forEach(key => {
          updateFields[`profile.${key}`] = (updateData.profile as any)[key];
        });
      }

      if (updateData.roles) {
        updateFields.roles = updateData.roles;
      }

      if (updateData.status) {
        updateFields.status = updateData.status;
      }

      if (updateData.authentication) {
        Object.keys(updateData.authentication).forEach(key => {
          updateFields[`authentication.${key}`] = (updateData.authentication as any)[key];
        });
      }

      if (updateData.settings) {
        Object.keys(updateData.settings).forEach(key => {
          updateFields[`settings.${key}`] = (updateData.settings as any)[key];
        });
      }

      await updateDoc(userRef, updateFields);
    } catch (error) {
      console.error('Error updating user:', error);
      throw new Error('Failed to update user');
    }
  }

  /**
   * Update user roles
   */
  static async updateUserRoles(userId: string, roles: UserRole[]): Promise<void> {
    try {
      // Validate roles
      if (!roles || roles.length === 0) {
        throw new Error('User must have at least one role');
      }

      // Check if roles are valid
      const validRoles: UserRole[] = ['customer', 'driver', 'admin', 'office_manager', 'support'];
      const invalidRoles = roles.filter(role => !validRoles.includes(role));
      
      if (invalidRoles.length > 0) {
        throw new Error(`Invalid roles: ${invalidRoles.join(', ')}`);
      }

      await updateDoc(doc(db, this.COLLECTION, userId), {
        roles,
        'profile.updatedAt': serverTimestamp(),
      });
    } catch (error) {
      console.error('Error updating user roles:', error);
      throw new Error('Failed to update user roles');
    }
  }

  /**
   * Update user status
   */
  static async updateUserStatus(userId: string, status: UserDocument['status']): Promise<void> {
    try {
      await updateDoc(doc(db, this.COLLECTION, userId), {
        status,
        'profile.updatedAt': serverTimestamp(),
      });
    } catch (error) {
      console.error('Error updating user status:', error);
      throw new Error('Failed to update user status');
    }
  }

  /**
   * Delete a user (hard delete - removes from database)
   */
  static async deleteUser(userId: string): Promise<void> {
    try {
      // Hard delete - remove user from database
      await deleteDoc(doc(db, this.COLLECTION, userId));
    } catch (error) {
      console.error('Error deleting user:', error);
      throw new Error('Failed to delete user');
    }
  }

  /**
   * Hard delete a user (use with caution)
   */
  static async hardDeleteUser(userId: string): Promise<void> {
    try {
      await deleteDoc(doc(db, this.COLLECTION, userId));
    } catch (error) {
      console.error('Error hard deleting user:', error);
      throw new Error('Failed to hard delete user');
    }
  }

  /**
   * Bulk update users
   */
  static async bulkUpdateUsers(userIds: string[], updateData: UpdateUserData): Promise<void> {
    try {
      const batch = writeBatch(db);
      
      userIds.forEach(userId => {
        const userRef = doc(db, this.COLLECTION, userId);
        const updateFields: any = {
          'profile.updatedAt': serverTimestamp(),
        };

        if (updateData.profile) {
          Object.keys(updateData.profile).forEach(key => {
            updateFields[`profile.${key}`] = (updateData.profile as any)[key];
          });
        }

        if (updateData.roles) {
          updateFields.roles = updateData.roles;
        }

        if (updateData.status) {
          updateFields.status = updateData.status;
        }

        batch.update(userRef, updateFields);
      });

      await batch.commit();
    } catch (error) {
      console.error('Error bulk updating users:', error);
      throw new Error('Failed to bulk update users');
    }
  }

  /**
   * Get user statistics
   */
  static async getUserStats(): Promise<UserStats> {
    try {
      const users = await this.getUsers({}, undefined, 1000); // Get all users for stats
      
      const stats: UserStats = {
        totalUsers: users.users.length,
        activeUsers: 0,
        inactiveUsers: 0,
        suspendedUsers: 0,
        customers: 0,
        drivers: 0,
        admins: 0,
        officeManagers: 0,
        supportUsers: 0,
        verifiedUsers: 0,
        unverifiedUsers: 0,
      };

      users.users.forEach(user => {
        // Count by status
        switch (user.status) {
          case 'active':
            stats.activeUsers++;
            break;
          case 'inactive':
            stats.inactiveUsers++;
            break;
          case 'suspended':
            stats.suspendedUsers++;
            break;
        }

        // Count by roles
        user.roles.forEach(role => {
          switch (role) {
            case 'customer':
              stats.customers++;
              break;
            case 'driver':
              stats.drivers++;
              break;
            case 'admin':
              stats.admins++;
              break;
            case 'office_manager':
              stats.officeManagers++;
              break;
            case 'support':
              stats.supportUsers++;
              break;
          }
        });

        // Count verification status
        if (user.authentication.phoneVerified) {
          stats.verifiedUsers++;
        } else {
          stats.unverifiedUsers++;
        }
      });

      return stats;
    } catch (error) {
      console.error('Error getting user stats:', error);
      throw new Error('Failed to get user statistics');
    }
  }

  /**
   * Verify user phone number
   */
  static async verifyUserPhone(userId: string): Promise<void> {
    try {
      await updateDoc(doc(db, this.COLLECTION, userId), {
        'authentication.phoneVerified': true,
        'profile.updatedAt': serverTimestamp(),
      });
    } catch (error) {
      console.error('Error verifying user phone:', error);
      throw new Error('Failed to verify user phone');
    }
  }

  /**
   * Verify user email
   */
  static async verifyUserEmail(userId: string): Promise<void> {
    try {
      await updateDoc(doc(db, this.COLLECTION, userId), {
        'authentication.emailVerified': true,
        'profile.updatedAt': serverTimestamp(),
      });
    } catch (error) {
      console.error('Error verifying user email:', error);
      throw new Error('Failed to verify user email');
    }
  }

  /**
   * Get users by role
   */
  static async getUsersByRole(role: UserRole): Promise<UserDocument[]> {
    try {
      const users = await this.getUsers({ role }, undefined, 1000);
      return users.users;
    } catch (error) {
      console.error('Error getting users by role:', error);
      throw new Error('Failed to get users by role');
    }
  }

  /**
   * Get users by status
   */
  static async getUsersByStatus(status: UserDocument['status']): Promise<UserDocument[]> {
    try {
      const users = await this.getUsers({ status }, undefined, 1000);
      return users.users;
    } catch (error) {
      console.error('Error getting users by status:', error);
      throw new Error('Failed to get users by status');
    }
  }

  /**
   * Export users data
   */
  static async exportUsers(filters: UserFilters = {}): Promise<UserDocument[]> {
    try {
      const users = await this.getUsers(filters, undefined, 10000); // Get more users for export
      return users.users;
    } catch (error) {
      console.error('Error exporting users:', error);
      throw new Error('Failed to export users');
    }
  }

  // Private helper methods
  private static validateUserData(userData: CreateUserData): void {
    if (!userData.profile.firstName || !userData.profile.lastName) {
      throw new Error('First name and last name are required');
    }

    if (!userData.profile.phone) {
      throw new Error('Phone number is required');
    }

    if (!userData.roles || userData.roles.length === 0) {
      throw new Error('At least one role is required');
    }

    // Validate phone number format (basic validation)
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    if (!phoneRegex.test(userData.profile.phone)) {
      throw new Error('Invalid phone number format');
    }

    // Validate email if provided
    if (userData.profile.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(userData.profile.email)) {
        throw new Error('Invalid email format');
      }
    }
  }

  private static getDateFilter(dateRange: string): Timestamp | null {
    const now = new Date();
    
    switch (dateRange) {
      case 'today':
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        return Timestamp.fromDate(today);
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        return Timestamp.fromDate(weekAgo);
      case 'month':
        const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
        return Timestamp.fromDate(monthAgo);
      case 'year':
        const yearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
        return Timestamp.fromDate(yearAgo);
      default:
        return null;
    }
  }
}
