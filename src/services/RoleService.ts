import { doc, updateDoc, getDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { UserRole, UserDocument } from '@/types/database';
import { AuthService } from './AuthService';

export interface RolePermissions {
  canAccessDashboard: boolean;
  canManageUsers: boolean;
  canManageDrivers: boolean;
  canManageOrders: boolean;
  canViewAnalytics: boolean;
  canManagePayments: boolean;
  canManageSettings: boolean;
  canViewReports: boolean;
  canManageSupport: boolean;
  canVerifyDrivers: boolean;
  canAssignOrders: boolean;
  canProcessPayments: boolean;
}

// Define role permissions
const ROLE_PERMISSIONS: Record<UserRole, RolePermissions> = {
  customer: {
    canAccessDashboard: false,
    canManageUsers: false,
    canManageDrivers: false,
    canManageOrders: false,
    canViewAnalytics: false,
    canManagePayments: false,
    canManageSettings: false,
    canViewReports: false,
    canManageSupport: false,
    canVerifyDrivers: false,
    canAssignOrders: false,
    canProcessPayments: false,
  },
  driver: {
    canAccessDashboard: true,
    canManageUsers: false,
    canManageDrivers: false,
    canManageOrders: true, // Only their own orders
    canViewAnalytics: false,
    canManagePayments: false,
    canManageSettings: false,
    canViewReports: false,
    canManageSupport: false,
    canVerifyDrivers: false,
    canAssignOrders: false,
    canProcessPayments: false,
  },
  office_manager: {
    canAccessDashboard: true,
    canManageUsers: true,
    canManageDrivers: true,
    canManageOrders: true,
    canViewAnalytics: true,
    canManagePayments: true,
    canManageSettings: false,
    canViewReports: true,
    canManageSupport: true,
    canVerifyDrivers: true,
    canAssignOrders: true,
    canProcessPayments: true,
  },
  support: {
    canAccessDashboard: true,
    canManageUsers: false,
    canManageDrivers: false,
    canManageOrders: true,
    canViewAnalytics: false,
    canManagePayments: false,
    canManageSettings: false,
    canViewReports: false,
    canManageSupport: true,
    canVerifyDrivers: false,
    canAssignOrders: false,
    canProcessPayments: false,
  },
  admin: {
    canAccessDashboard: true,
    canManageUsers: true,
    canManageDrivers: true,
    canManageOrders: true,
    canViewAnalytics: true,
    canManagePayments: true,
    canManageSettings: true,
    canViewReports: true,
    canManageSupport: true,
    canVerifyDrivers: true,
    canAssignOrders: true,
    canProcessPayments: true,
  },
};

export class RoleService {
  /**
   * Get permissions for a specific role
   */
  static getPermissionsForRole(role: UserRole): RolePermissions {
    return ROLE_PERMISSIONS[role];
  }

  /**
   * Get combined permissions for multiple roles
   */
  static getCombinedPermissions(roles: UserRole[]): RolePermissions {
    const combinedPermissions: RolePermissions = {
      canAccessDashboard: false,
      canManageUsers: false,
      canManageDrivers: false,
      canManageOrders: false,
      canViewAnalytics: false,
      canManagePayments: false,
      canManageSettings: false,
      canViewReports: false,
      canManageSupport: false,
      canVerifyDrivers: false,
      canAssignOrders: false,
      canProcessPayments: false,
    };

    // Combine permissions from all roles (OR operation)
    roles.forEach(role => {
      const rolePermissions = ROLE_PERMISSIONS[role];
      Object.keys(combinedPermissions).forEach(permission => {
        const key = permission as keyof RolePermissions;
        combinedPermissions[key] = combinedPermissions[key] || rolePermissions[key];
      });
    });

    return combinedPermissions;
  }

  /**
   * Check if user has specific permission
   */
  static hasPermission(userRoles: UserRole[], permission: keyof RolePermissions): boolean {
    const permissions = this.getCombinedPermissions(userRoles);
    return permissions[permission];
  }

  /**
   * Check if user has any of the specified roles
   */
  static hasAnyRole(userRoles: UserRole[], requiredRoles: UserRole[]): boolean {
    return requiredRoles.some(role => userRoles.includes(role));
  }

  /**
   * Check if user has all of the specified roles
   */
  static hasAllRoles(userRoles: UserRole[], requiredRoles: UserRole[]): boolean {
    return requiredRoles.every(role => userRoles.includes(role));
  }

  /**
   * Get the highest priority role (for display purposes)
   */
  static getPrimaryRole(roles: UserRole[]): UserRole {
    const rolePriority: Record<UserRole, number> = {
      admin: 5,
      office_manager: 4,
      support: 3,
      driver: 2,
      customer: 1,
    };

    return roles.reduce((primary, current) => {
      return rolePriority[current] > rolePriority[primary] ? current : primary;
    }, roles[0] || 'customer');
  }

  /**
   * Add role to user (admin only)
   */
  static async addRoleToUser(userId: string, role: UserRole): Promise<void> {
    const currentUser = AuthService.getCurrentUser();
    if (!currentUser) {
      throw new Error('Not authenticated');
    }

    // Get current user's profile to check permissions
    const currentUserDoc = await getDoc(doc(db, 'users', currentUser.uid));
    if (!currentUserDoc.exists()) {
      throw new Error('Current user profile not found');
    }

    const currentUserData = currentUserDoc.data() as UserDocument;
    if (!this.hasPermission(currentUserData.roles, 'canManageUsers')) {
      throw new Error('Insufficient permissions to manage user roles');
    }

    // Get target user's current roles
    const userDoc = await getDoc(doc(db, 'users', userId));
    if (!userDoc.exists()) {
      throw new Error('User not found');
    }

    const userData = userDoc.data() as UserDocument;
    const updatedRoles = [...new Set([...userData.roles, role])]; // Remove duplicates

    // Update user roles
    await updateDoc(doc(db, 'users', userId), {
      roles: updatedRoles,
      'profile.updatedAt': serverTimestamp()
    });
  }

  /**
   * Remove role from user (admin only)
   */
  static async removeRoleFromUser(userId: string, role: UserRole): Promise<void> {
    const currentUser = AuthService.getCurrentUser();
    if (!currentUser) {
      throw new Error('Not authenticated');
    }

    // Get current user's profile to check permissions
    const currentUserDoc = await getDoc(doc(db, 'users', currentUser.uid));
    if (!currentUserDoc.exists()) {
      throw new Error('Current user profile not found');
    }

    const currentUserData = currentUserDoc.data() as UserDocument;
    if (!this.hasPermission(currentUserData.roles, 'canManageUsers')) {
      throw new Error('Insufficient permissions to manage user roles');
    }

    // Get target user's current roles
    const userDoc = await getDoc(doc(db, 'users', userId));
    if (!userDoc.exists()) {
      throw new Error('User not found');
    }

    const userData = userDoc.data() as UserDocument;
    const updatedRoles = userData.roles.filter(r => r !== role);

    // Ensure user always has at least the customer role
    if (updatedRoles.length === 0) {
      updatedRoles.push('customer');
    }

    // Update user roles
    await updateDoc(doc(db, 'users', userId), {
      roles: updatedRoles,
      'profile.updatedAt': serverTimestamp()
    });
  }

  /**
   * Set user roles (admin only)
   */
  static async setUserRoles(userId: string, roles: UserRole[]): Promise<void> {
    const currentUser = AuthService.getCurrentUser();
    if (!currentUser) {
      throw new Error('Not authenticated');
    }

    // Get current user's profile to check permissions
    const currentUserDoc = await getDoc(doc(db, 'users', currentUser.uid));
    if (!currentUserDoc.exists()) {
      throw new Error('Current user profile not found');
    }

    const currentUserData = currentUserDoc.data() as UserDocument;
    if (!this.hasPermission(currentUserData.roles, 'canManageUsers')) {
      throw new Error('Insufficient permissions to manage user roles');
    }

    // Validate roles
    const validRoles: UserRole[] = ['customer', 'driver', 'admin', 'office_manager', 'support'];
    const invalidRoles = roles.filter(role => !validRoles.includes(role));
    if (invalidRoles.length > 0) {
      throw new Error(`Invalid roles: ${invalidRoles.join(', ')}`);
    }

    // Ensure user has at least customer role
    const finalRoles = roles.length > 0 ? roles : ['customer'];

    // Update user roles
    await updateDoc(doc(db, 'users', userId), {
      roles: finalRoles,
      'profile.updatedAt': serverTimestamp()
    });
  }

  /**
   * Check if current user can perform action on target user
   */
  static async canManageUser(targetUserId: string): Promise<boolean> {
    const currentUser = AuthService.getCurrentUser();
    if (!currentUser) {
      return false;
    }

    // Users can always manage themselves (limited actions)
    if (currentUser.uid === targetUserId) {
      return true;
    }

    // Get current user's profile
    const currentUserDoc = await getDoc(doc(db, 'users', currentUser.uid));
    if (!currentUserDoc.exists()) {
      return false;
    }

    const currentUserData = currentUserDoc.data() as UserDocument;
    return this.hasPermission(currentUserData.roles, 'canManageUsers');
  }

  /**
   * Get role display name
   */
  static getRoleDisplayName(role: UserRole): string {
    const displayNames: Record<UserRole, string> = {
      customer: 'Customer',
      driver: 'Driver',
      office_manager: 'Office Manager',
      support: 'Support Agent',
      admin: 'Administrator',
    };

    return displayNames[role] || role;
  }

  /**
   * Get role description
   */
  static getRoleDescription(role: UserRole): string {
    const descriptions: Record<UserRole, string> = {
      customer: 'Can book rides and manage their profile',
      driver: 'Can accept rides and manage their driver profile',
      office_manager: 'Can manage operations, users, and view analytics',
      support: 'Can help customers and manage support tickets',
      admin: 'Full system access and management',
    };

    return descriptions[role] || '';
  }

  /**
   * Check if role can be assigned by current user
   */
  static canAssignRole(currentUserRoles: UserRole[], roleToAssign: UserRole): boolean {
    // Only admins can assign admin role
    if (roleToAssign === 'admin') {
      return this.hasAnyRole(currentUserRoles, ['admin']);
    }

    // Admins and office managers can assign other roles
    return this.hasAnyRole(currentUserRoles, ['admin', 'office_manager']);
  }
}

export default RoleService;
