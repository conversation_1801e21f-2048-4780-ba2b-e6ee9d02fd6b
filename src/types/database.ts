// types/database.ts
import { Timestamp, GeoPoint } from 'firebase/firestore'

export interface DatabaseSchema {
  // Core Collections
  users: UserDocument
  orders: OrderDocument
  drivers: DriverDocument
  payments: PaymentDocument
  
  // Supporting Collections
  cities: CityDocument
  categories: CategoryDocument
  settings: SettingsDocument
  notifications: NotificationDocument
  
  // Admin Collections
  analytics: AnalyticsDocument
  reports: ReportDocument
  support_tickets: SupportTicketDocument
}

// User Management
export interface UserDocument {
  id: string
  profile: {
    firstName: string
    lastName: string
    phone: string
    email?: string
    avatar?: string
    gender: 'male' | 'female'
    birthDate?: string
    language: 'en' | 'ar'
    createdAt: Timestamp
    updatedAt: Timestamp
  }
  roles: UserRole[]
  authentication: {
    phoneVerified: boolean
    emailVerified: boolean
    providers: AuthProvider[]
    lastLogin: Timestamp
    activeUntil: Timestamp
  }
  settings: {
    notifications: NotificationSettings
    privacy: PrivacySettings
    theme: 'light' | 'dark'
  }
  stats: {
    totalOrders: number
    totalSpent: number
    averageRating: number
    joinedAt: Timestamp
  }
  status: 'active' | 'inactive' | 'suspended'
}

export interface DriverDocument {
  id: string // Same as user ID
  personalInfo: {
    firstName: string
    lastName: string
    phone: string
    email: string
    nationalId: string
    licenseNumber: string
    licenseExpiry: string
    emergencyContact: ContactInfo
  }
  vehicle: {
    make: string
    model: string
    year: number
    color: string
    plateNumber: string
    licenseNumber: string
    licenseExpiry: string
    insurance: InsuranceInfo
  }
  documents: {
    nationalIdFront: string
    nationalIdBack: string
    drivingLicenseFront: string
    drivingLicenseBack: string
    vehicleLicense: string
    vehicleImages: string[]
    insuranceCertificate: string
    backgroundCheck: string
  }
  verification: {
    status: 'pending' | 'approved' | 'rejected'
    verifiedAt?: Timestamp
    verifiedBy?: string
    notes?: string
  }
  location: {
    current: GeoPoint
    heading: number
    speed: number
    accuracy: number
    lastUpdated: Timestamp
  }
  status: {
    online: boolean
    available: boolean
    currentOrder?: string
    lastSeen: Timestamp
  }
  earnings: {
    totalEarnings: number
    pendingPayout: number
    completedTrips: number
    rating: {
      average: number
      count: number
      breakdown: RatingBreakdown
    }
  }
  schedule: {
    workingHours: WorkingHours
    preferredAreas: GeoArea[]
  }
}

export interface OrderDocument {
  id: string
  customer: {
    id: string
    name: string
    phone: string
    avatar?: string
  }
  driver?: {
    id: string
    name: string
    phone: string
    avatar?: string
    vehicle: VehicleInfo
    location: GeoPoint
  }
  service: {
    categoryId: string
    categoryName: string
    type: 'standard' | 'premium' | 'economy'
  }
  locations: {
    pickup: {
      address: string
      coordinates: GeoPoint
      placeId: string
      details?: AddressDetails
    }
    destination: {
      address: string
      coordinates: GeoPoint
      placeId: string
      details?: AddressDetails
    }
    route?: {
      distance: number
      duration: number
      encodedPolyline: string
    }
  }
  pricing: {
    baseFare: number
    distanceFare: number
    timeFare: number
    surcharge: number
    discount: number
    total: number
    currency: 'JOD'
    breakdown: PriceBreakdown
  }
  status: {
    current: OrderStatus
    timeline: OrderEvent[]
    estimatedArrival?: Timestamp
    estimatedCompletion?: Timestamp
  }
  payment: {
    method: 'cash' | 'card' | 'wallet'
    status: 'pending' | 'completed' | 'failed' | 'refunded'
    transactionId?: string
    processedAt?: Timestamp
    cardDetails?: CardInfo
  }
  tracking: {
    realTimeEnabled: boolean
    driverMovements: MovementPoint[]
    estimatedRoute: RoutePoint[]
    startTime?: Timestamp
    endTime?: Timestamp
  }
  ratings: {
    customerRating?: {
      stars: number
      comment?: string
      ratedAt: Timestamp
    }
    driverRating?: {
      stars: number
      comment?: string
      ratedAt: Timestamp
    }
  }
  metadata: {
    createdAt: Timestamp
    updatedAt: Timestamp
    completedAt?: Timestamp
    cancelledAt?: Timestamp
    cancelledBy?: string
    cancellationReason?: string
    source: 'mobile' | 'web' | 'api'
    version: string
  }
}

export interface PaymentDocument {
  id: string
  userId: string
  orderId?: string
  type: 'payment' | 'refund' | 'payout' | 'topup'
  amount: number
  currency: 'JOD'
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  method: {
    type: 'card' | 'cash' | 'wallet'
    cardDetails?: {
      last4: string
      brand: string
      expiryMonth: number
      expiryYear: number
    }
  }
  gateway: {
    provider: 'gatetopay' | 'cash'
    transactionId?: string
    gatewayResponse?: any
  }
  metadata: {
    createdAt: Timestamp
    completedAt?: Timestamp
    description: string
    reference: string
  }
}

// Supporting Types
export type UserRole = 'customer' | 'driver' | 'admin' | 'office_manager' | 'support'
export type OrderStatus = 'pending' | 'searching' | 'assigned' | 'driver_arriving' | 'driver_arrived' | 'picked_up' | 'in_progress' | 'completed' | 'cancelled'
export type AuthProvider = 'phone' | 'google' | 'facebook' | 'apple'

export interface NotificationDocument {
  id: string
  userId: string
  type: 'order_update' | 'payment' | 'promotion' | 'system'
  title: string
  body: string
  data?: Record<string, any>
  read: boolean
  createdAt: Timestamp
  scheduledFor?: Timestamp
}

export interface SettingsDocument {
  id: string
  category: 'general' | 'pricing' | 'notifications' | 'features'
  settings: Record<string, any>
  updatedAt: Timestamp
  updatedBy: string
}

export interface CityDocument {
  id: string
  name: string
  country: string
  coordinates: GeoPoint
  isActive: boolean
}

export interface CategoryDocument {
  id: string
  name: string
  description: string
  basePrice: number
  pricePerKm: number
  pricePerMinute: number
  isActive: boolean
}

// Helper Types
export interface NotificationSettings {
  orderUpdates: boolean
  promotions: boolean
  system: boolean
}

export interface PrivacySettings {
  shareLocation: boolean
  showProfile: boolean
}

export interface ContactInfo {
  name: string
  phone: string
  relationship?: string
}

export interface InsuranceInfo {
  provider: string
  policyNumber: string
  expiryDate: string
}

export interface RatingBreakdown {
  5: number
  4: number
  3: number
  2: number
  1: number
}

export interface WorkingHours {
  monday: { start: string; end: string; active: boolean }
  tuesday: { start: string; end: string; active: boolean }
  wednesday: { start: string; end: string; active: boolean }
  thursday: { start: string; end: string; active: boolean }
  friday: { start: string; end: string; active: boolean }
  saturday: { start: string; end: string; active: boolean }
  sunday: { start: string; end: string; active: boolean }
}

export interface GeoArea {
  name: string
  coordinates: GeoPoint[]
}

export interface VehicleInfo {
  make: string
  model: string
  year: number
  color: string
  plateNumber: string
}

export interface AddressDetails {
  city: string
  district: string
  building?: string
  floor?: string
  apartment?: string
}

export interface PriceBreakdown {
  [key: string]: number
}

export interface OrderEvent {
  status: OrderStatus
  timestamp: Timestamp
  notes?: string
}

export interface CardInfo {
  last4: string
  brand: string
  expiryMonth: number
  expiryYear: number
}

export interface MovementPoint {
  coordinates: GeoPoint
  timestamp: Timestamp
  speed: number
  heading: number
}

export interface RoutePoint {
  coordinates: GeoPoint
  estimatedTime: Timestamp
}

export interface AnalyticsDocument {
  id: string
  date: string
  metrics: Record<string, number>
  createdAt: Timestamp
}

export interface ReportDocument {
  id: string
  type: string
  title: string
  data: any
  generatedAt: Timestamp
  generatedBy: string
}

export interface SupportTicketDocument {
  id: string
  userId: string
  subject: string
  description: string
  status: 'open' | 'pending' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  assignedTo?: string
  createdAt: Timestamp
  updatedAt: Timestamp
}
