import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { UserService, UserFilters, UserStats, CreateUserData, UpdateUserData } from '@/services/UserService';
import { UserDocument, UserRole } from '@/types/database';
import { QueryDocumentSnapshot, DocumentData } from 'firebase/firestore';

export interface UseUsersReturn {
  // State
  users: UserDocument[];
  loading: boolean;
  error: string | null;
  stats: UserStats | null;
  filters: UserFilters;
  hasMore: boolean;
  lastDoc: QueryDocumentSnapshot<DocumentData> | undefined;
  
  // Actions
  setFilters: (filters: UserFilters) => void;
  searchUsers: (searchTerm: string) => Promise<void>;
  createUser: (userData: CreateUserData) => Promise<string>;
  updateUser: (userId: string, updateData: UpdateUserData) => Promise<void>;
  updateUserRoles: (userId: string, roles: UserRole[]) => Promise<void>;
  updateUserStatus: (userId: string, status: UserDocument['status']) => Promise<void>;
  deleteUser: (userId: string) => Promise<void>;
  verifyUserPhone: (userId: string) => Promise<void>;
  verifyUserEmail: (userId: string) => Promise<void>;
  bulkUpdateUsers: (userIds: string[], updateData: UpdateUserData) => Promise<void>;
  loadMoreUsers: () => Promise<void>;
  refreshUsers: () => Promise<void>;
  exportUsers: () => Promise<UserDocument[]>;
  clearError: () => void;
}

export function useUsers(initialFilters: UserFilters = {}): UseUsersReturn {
  const [users, setUsers] = useState<UserDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<UserStats | null>(null);
  const [filters, setFilters] = useState<UserFilters>(initialFilters);
  const [hasMore, setHasMore] = useState(false);
  const [lastDoc, setLastDoc] = useState<QueryDocumentSnapshot<DocumentData> | undefined>();
  
  // Refs to prevent unnecessary re-renders
  const isInitialized = useRef(false);
  const debounceTimer = useRef<NodeJS.Timeout>();
  const filtersRef = useRef(filters);
  const lastDocRef = useRef(lastDoc);

  // Update refs when state changes
  useEffect(() => {
    filtersRef.current = filters;
  }, [filters]);

  useEffect(() => {
    lastDocRef.current = lastDoc;
  }, [lastDoc]);

  // Memoize the loadUsers function to prevent infinite loops
  const loadUsers = useCallback(async (reset?: boolean) => {
    try {
      setLoading(true);
      setError(null);

      const currentFilters = filtersRef.current;
      const currentLastDoc = reset ? undefined : lastDocRef.current;

      const result = await UserService.getUsers(
        currentFilters,
        currentLastDoc,
        50
      );

      if (reset) {
        setUsers(result.users);
        setLastDoc(result.lastDoc);
      } else {
        setUsers(prev => [...prev, ...result.users]);
        setLastDoc(result.lastDoc);
      }

      setHasMore(result.hasMore);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load users');
    } finally {
      setLoading(false);
    }
  }, []); // Empty dependency array since we use refs

  // Memoize the loadStats function
  const loadStats = useCallback(async () => {
    try {
      const userStats = await UserService.getUserStats();
      setStats(userStats);
    } catch (err) {
      console.error('Failed to load user stats:', err);
    }
  }, []);

  // Initial load - only run once
  useEffect(() => {
    if (!isInitialized.current) {
      isInitialized.current = true;
      // Use a local function to avoid dependency issues
      const initializeData = async () => {
        try {
          setLoading(true);
          setError(null);

          const result = await UserService.getUsers(filters, undefined, 50);
          setUsers(result.users);
          setLastDoc(result.lastDoc);
          setHasMore(result.hasMore);
          
          const userStats = await UserService.getUserStats();
          setStats(userStats);
        } catch (err) {
          setError(err instanceof Error ? err.message : 'Failed to load initial data');
        } finally {
          setLoading(false);
        }
      };
      
      initializeData();
    }
  }, []); // Empty dependency array for initial load only

  // Debounced filter change handler
  const handleFiltersChange = useCallback((newFilters: UserFilters) => {
    // Clear existing timer
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    // Set new timer for debounced update
    debounceTimer.current = setTimeout(() => {
      setFilters(newFilters);
    }, 300); // 300ms debounce
  }, []);

  // Reload when filters change - use memoized filters and debouncing
  useEffect(() => {
    if (isInitialized.current && users.length > 0) { // Only reload if we already have users
      loadUsers(true);
    }
  }, [filters]); // Remove loadUsers from dependencies since it's stable

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, []);

  // Search users
  const searchUsers = useCallback(async (searchTerm: string) => {
    try {
      setLoading(true);
      setError(null);

      if (!searchTerm.trim()) {
        await loadUsers(true);
        return;
      }

      const searchResults = await UserService.searchUsers(searchTerm);
      setUsers(searchResults);
      setHasMore(false);
      setLastDoc(undefined);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to search users');
    } finally {
      setLoading(false);
    }
  }, []); // Remove loadUsers dependency since it's stable

  // Create user
  const createUser = useCallback(async (userData: CreateUserData): Promise<string> => {
    try {
      setError(null);
      const userId = await UserService.createUser(userData);
      
      // Refresh users list
      await loadUsers(true);
      await loadStats();
      
      return userId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create user';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []); // Remove dependencies since functions are stable

  // Update user
  const updateUser = useCallback(async (userId: string, updateData: UpdateUserData): Promise<void> => {
    try {
      setError(null);
      await UserService.updateUser(userId, updateData);
      
      // Update local state
      setUsers(prev => prev.map(user => 
        user.id === userId 
          ? { 
              ...user, 
              ...(updateData.profile && { profile: { ...user.profile, ...updateData.profile } }),
              ...(updateData.roles && { roles: updateData.roles }),
              ...(updateData.status && { status: updateData.status }),
              ...(updateData.authentication && { 
                authentication: { ...user.authentication, ...updateData.authentication }
              }),
              ...(updateData.settings && { 
                settings: { ...user.settings, ...updateData.settings }
              })
            }
          : user
      ));
      
      // Refresh stats if needed
      if (updateData.status || updateData.roles) {
        await loadStats();
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update user';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadStats]);

  // Update user roles
  const updateUserRoles = useCallback(async (userId: string, roles: UserRole[]): Promise<void> => {
    try {
      setError(null);
      await UserService.updateUserRoles(userId, roles);
      
      // Update local state
      setUsers(prev => prev.map(user => 
        user.id === userId ? { ...user, roles } : user
      ));
      
      // Refresh stats
      await loadStats();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update user roles';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadStats]);

  // Update user status
  const updateUserStatus = useCallback(async (userId: string, status: UserDocument['status']): Promise<void> => {
    try {
      setError(null);
      await UserService.updateUserStatus(userId, status);
      
      // Update local state
      setUsers(prev => prev.map(user => 
        user.id === userId ? { ...user, status } : user
      ));
      
      // Refresh stats
      await loadStats();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update user status';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadStats]);

  // Delete user
  const deleteUser = useCallback(async (userId: string): Promise<void> => {
    try {
      setError(null);
      await UserService.deleteUser(userId);
      
      // Remove user from local state
      setUsers(prev => prev.filter(user => user.id !== userId));
      
      // Refresh stats
      await loadStats();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete user';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadStats]);

  // Verify user phone
  const verifyUserPhone = useCallback(async (userId: string): Promise<void> => {
    try {
      setError(null);
      await UserService.verifyUserPhone(userId);
      
      // Update local state
      setUsers(prev => prev.map(user => 
        user.id === userId 
          ? { 
              ...user, 
              authentication: { ...user.authentication, phoneVerified: true } 
            }
          : user
      ));
      
      // Refresh stats
      await loadStats();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to verify user phone';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadStats]);

  // Verify user email
  const verifyUserEmail = useCallback(async (userId: string): Promise<void> => {
    try {
      setError(null);
      await UserService.verifyUserEmail(userId);
      
      // Update local state
      setUsers(prev => prev.map(user => 
        user.id === userId 
          ? { 
              ...user, 
              authentication: { ...user.authentication, emailVerified: true } 
            }
          : user
      ));
      
      // Refresh stats
      await loadStats();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to verify user email';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadStats]);

  // Bulk update users
  const bulkUpdateUsers = useCallback(async (userIds: string[], updateData: UpdateUserData): Promise<void> => {
    try {
      setError(null);
      await UserService.bulkUpdateUsers(userIds, updateData);
      
      // Update local state
      setUsers(prev => prev.map(user => 
        userIds.includes(user.id) 
          ? { 
              ...user, 
              ...(updateData.profile && { profile: { ...user.profile, ...updateData.profile } }),
              ...(updateData.roles && { roles: updateData.roles }),
              ...(updateData.status && { status: updateData.status }),
              ...(updateData.authentication && { 
                authentication: { ...user.authentication, ...updateData.authentication }
              }),
              ...(updateData.settings && { 
                settings: { ...user.settings, ...updateData.settings }
              })
            }
          : user
      ));
      
      // Refresh stats if needed
      if (updateData.status || updateData.roles) {
        await loadStats();
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to bulk update users';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadStats]);

  // Load more users (pagination)
  const loadMoreUsers = useCallback(async () => {
    if (!hasMore || loading) return;
    await loadUsers(false);
  }, [hasMore, loading]); // Remove loadUsers dependency since it's stable

  // Refresh users
  const refreshUsers = useCallback(async () => {
    await loadUsers(true);
    await loadStats();
  }, []); // Remove dependencies since functions are stable

  // Export users
  const exportUsers = useCallback(async (): Promise<UserDocument[]> => {
    try {
      setError(null);
      return await UserService.exportUsers(filters);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to export users';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [filters]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // State
    users,
    loading,
    error,
    stats,
    filters,
    hasMore,
    lastDoc,
    
    // Actions
    setFilters: handleFiltersChange, // Use debounced version
    searchUsers,
    createUser,
    updateUser,
    updateUserRoles,
    updateUserStatus,
    deleteUser,
    verifyUserPhone,
    verifyUserEmail,
    bulkUpdateUsers,
    loadMoreUsers,
    refreshUsers,
    exportUsers,
    clearError,
  };
}
