import { useState, useEffect, useCallback, useRef } from 'react';
import { DriverService, DriverFilters, DriverStats, CreateDriverData, UpdateDriverData } from '@/services/DriverService';
import { DriverDocument } from '@/types/database';
import { QueryDocumentSnapshot, DocumentData } from 'firebase/firestore';

export interface DriverUser {
  user: any;
  driverDoc: DriverDocument | null;
  hasDriverInfo: boolean;
}

export interface UseDriversReturn {
  // State
  drivers: DriverDocument[];
  driverUsers: DriverUser[];
  loading: boolean;
  error: string | null;
  stats: DriverStats | null;
  filters: DriverFilters;
  hasMore: boolean;
  lastDoc: QueryDocumentSnapshot<DocumentData> | undefined;
  
  // Actions
  setFilters: (filters: DriverFilters) => void;
  searchDrivers: (searchTerm: string) => Promise<void>;
  createDriver: (driverData: CreateDriverData) => Promise<string>;
  updateDriver: (id: string, data: UpdateDriverData) => Promise<void>;
  deleteDriver: (id: string) => Promise<void>;
  verifyDriver: (id: string, approved: boolean, notes?: string) => Promise<void>;
  updateDriverStatus: (id: string, online: boolean, available: boolean) => Promise<void>;
  loadMoreDrivers: () => Promise<void>;
  refreshDrivers: () => Promise<void>;
  exportDrivers: () => Promise<DriverDocument[]>;
  clearError: () => void;
}

export function useDrivers(initialFilters: DriverFilters = {}): UseDriversReturn {
  const [drivers, setDrivers] = useState<DriverDocument[]>([]);
  const [driverUsers, setDriverUsers] = useState<DriverUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<DriverStats | null>(null);
  const [filters, setFilters] = useState<DriverFilters>(initialFilters);
  const [hasMore, setHasMore] = useState(false);
  const [lastDoc, setLastDoc] = useState<QueryDocumentSnapshot<DocumentData> | undefined>();
  
  // Refs to prevent unnecessary re-renders
  const isInitialized = useRef(false);
  const debounceTimer = useRef<NodeJS.Timeout>();
  const filtersRef = useRef(filters);
  const lastDocRef = useRef(lastDoc);

  // Update refs when state changes
  useEffect(() => {
    filtersRef.current = filters;
  }, [filters]);

  useEffect(() => {
    lastDocRef.current = lastDoc;
  }, [lastDoc]);

  // Load drivers function
  const loadDrivers = useCallback(async (
    currentFilters: DriverFilters = filtersRef.current,
    currentLastDoc?: QueryDocumentSnapshot<DocumentData>,
    append: boolean = false
  ) => {
    try {
      setError(null);
      if (!append) {
        setLoading(true);
      }

      const result = await DriverService.getDrivers(currentFilters, currentLastDoc, 50);
      
      if (append) {
        setDrivers(prev => [...prev, ...result.drivers]);
      } else {
        setDrivers(result.drivers);
      }
      
      setLastDoc(result.lastDoc);
      setHasMore(result.hasMore);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load drivers';
      setError(errorMessage);
      if (!append) {
        setDrivers([]);
      }
    } finally {
      setLoading(false);
    }
  }, []);

  // Load stats function
  const loadStats = useCallback(async () => {
    try {
      const driverStats = await DriverService.getDriverStats();
      setStats(driverStats);
    } catch (err) {
      console.error('Failed to load driver stats:', err);
      // Don't set error for stats failure, just log it
    }
  }, []);

  // Load driver users function
  const loadDriverUsers = useCallback(async () => {
    try {
      setError(null);
      setLoading(true);
      const users = await DriverService.getDriverUsers();
      setDriverUsers(users);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load driver users';
      setError(errorMessage);
      setDriverUsers([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial load - only run once
  useEffect(() => {
    if (!isInitialized.current) {
      isInitialized.current = true;
      const initializeData = async () => {
        try {
          setLoading(true);
          setError(null);

          const result = await DriverService.getDrivers(filters, undefined, 50);
          setDrivers(result.drivers);
          setLastDoc(result.lastDoc);
          setHasMore(result.hasMore);
          
          const driverStats = await DriverService.getDriverStats();
          setStats(driverStats);
        } catch (err) {
          setError(err instanceof Error ? err.message : 'Failed to load initial data');
        } finally {
          setLoading(false);
        }
      };
      
      initializeData();
    }
  }, []); // Empty dependency array for initial load only

  // Handle filters change with debouncing
  const handleFiltersChange = useCallback((newFilters: DriverFilters) => {
    setFilters(newFilters);
    setLastDoc(undefined);
    
    // Clear existing timer
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }
    
    // Set new timer for debounced search
    debounceTimer.current = setTimeout(() => {
      loadDrivers(newFilters, undefined, false);
    }, 300);
  }, [loadDrivers]);

  // Search drivers
  const searchDrivers = useCallback(async (searchTerm: string): Promise<void> => {
    const searchFilters = { ...filters, search: searchTerm };
    setFilters(searchFilters);
    await loadDrivers(searchFilters, undefined, false);
  }, [filters, loadDrivers]);

  // Create driver
  const createDriver = useCallback(async (driverData: CreateDriverData): Promise<string> => {
    try {
      setError(null);
      const driverId = await DriverService.createDriver(driverData);
      
      // Refresh drivers list and stats
      await Promise.all([
        loadDrivers(filtersRef.current, undefined, false),
        loadStats()
      ]);
      
      return driverId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create driver';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadDrivers, loadStats]);

  // Update driver
  const updateDriver = useCallback(async (id: string, data: UpdateDriverData): Promise<void> => {
    try {
      setError(null);
      await DriverService.updateDriver(id, data);
      
      // Update local state optimistically
      setDrivers(prev => prev.map(driver => 
        driver.id === id 
          ? { 
              ...driver, 
              ...data,
              personalInfo: { ...driver.personalInfo, ...data.personalInfo },
              vehicle: { ...driver.vehicle, ...data.vehicle },
              verification: { ...driver.verification, ...data.verification },
              status: { ...driver.status, ...data.status }
            } as DriverDocument
          : driver
      ));
      
      // Refresh stats
      await loadStats();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update driver';
      setError(errorMessage);
      // Refresh drivers to revert optimistic update
      await loadDrivers(filtersRef.current, undefined, false);
      throw new Error(errorMessage);
    }
  }, [loadDrivers, loadStats]);

  // Delete driver
  const deleteDriver = useCallback(async (id: string): Promise<void> => {
    try {
      setError(null);
      await DriverService.deleteDriver(id);
      
      // Remove from local state
      setDrivers(prev => prev.filter(driver => driver.id !== id));
      
      // Refresh stats
      await loadStats();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete driver';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadStats]);

  // Verify driver
  const verifyDriver = useCallback(async (id: string, approved: boolean, notes?: string): Promise<void> => {
    try {
      setError(null);
      await DriverService.verifyDriver(id, approved, notes);
      
      // Update local state
      setDrivers(prev => prev.map(driver => 
        driver.id === id 
          ? { 
              ...driver, 
              verification: {
                ...driver.verification,
                status: approved ? 'approved' : 'rejected',
                notes: notes || '',
                verifiedAt: new Date() as any
              }
            }
          : driver
      ));
      
      // Refresh stats
      await loadStats();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to verify driver';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadStats]);

  // Update driver status
  const updateDriverStatus = useCallback(async (id: string, online: boolean, available: boolean): Promise<void> => {
    try {
      setError(null);
      await DriverService.updateDriverStatus(id, online, available);
      
      // Update local state
      setDrivers(prev => prev.map(driver => 
        driver.id === id 
          ? { 
              ...driver, 
              status: {
                ...driver.status,
                online,
                available,
                lastSeen: new Date() as any
              }
            }
          : driver
      ));
      
      // Refresh stats
      await loadStats();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update driver status';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadStats]);

  // Load more drivers
  const loadMoreDrivers = useCallback(async (): Promise<void> => {
    if (!hasMore || loading) return;
    await loadDrivers(filtersRef.current, lastDocRef.current, true);
  }, [hasMore, loading, loadDrivers]);

  // Refresh drivers
  const refreshDrivers = useCallback(async (): Promise<void> => {
    setLastDoc(undefined);
    await Promise.all([
      loadDrivers(filtersRef.current, undefined, false),
      loadStats()
    ]);
  }, [loadDrivers, loadStats]);

  // Export drivers
  const exportDrivers = useCallback(async (): Promise<DriverDocument[]> => {
    try {
      setError(null);
      return await DriverService.exportDrivers(filters);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to export drivers';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [filters]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // State
    drivers,
    driverUsers,
    loading,
    error,
    stats,
    filters,
    hasMore,
    lastDoc,

    // Actions
    setFilters: handleFiltersChange,
    searchDrivers,
    createDriver,
    updateDriver,
    deleteDriver,
    verifyDriver,
    updateDriverStatus,
    loadMoreDrivers,
    refreshDrivers,
    exportDrivers,
    loadDriverUsers,
    clearError,
  };
}
