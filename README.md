# 🚕 YellowTaxi v2 - Next.js 15 + Firebase Rebuild

Modern ride-sharing platform built with Next.js 15, Firebase, and TypeScript. Complete rebuild of the Laravel-based YellowTaxi Dashboard with enhanced real-time capabilities and scalable architecture.

## 🎯 Project Overview

### Mission
Rebuild the Laravel-based YellowTaxi Dashboard as a modern Next.js 15 + Firebase application with seamless migration of 2000+ existing users and zero downtime deployment.

### Tech Stack
- **Frontend**: Next.js 15 + React 18 + TypeScript + TailwindCSS v4
- **Backend**: Firebase (Firestore, Auth, Functions, Storage)
- **Database**: Firestore (NoSQL) + MySQL migration
- **Auth**: Firebase Authentication (Phone/OTP, Social)
- **Real-time**: Firestore real-time listeners
- **Payment**: GateToPay integration via Cloud Functions
- **Maps**: Google Maps API
- **SMS**: josms.net gateway
- **UI**: Shadcn/ui components

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Firebase account
- Google Maps API key

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd v2-YellowTaxi-Web
```

2. **Install dependencies**
```bash
npm install
```

3. **Setup environment variables**
```bash
cp .env.example .env.local
# Edit .env.local with your Firebase and API keys
```

4. **Run the development server**
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
src/
├── app/                          # Next.js 15 App Router
│   ├── (auth)/                   # Authentication routes
│   │   ├── login/
│   │   ├── register/
│   │   └── verify-otp/
│   ├── (dashboard)/              # Dashboard routes
│   │   ├── dashboard/
│   │   ├── users/
│   │   ├── drivers/
│   │   ├── orders/
│   │   ├── payments/
│   │   └── settings/
│   └── api/                      # API routes
├── components/                   # React components
│   ├── ui/                       # Shadcn/ui components
│   ├── auth/                     # Authentication components
│   ├── dashboard/                # Dashboard components
│   ├── maps/                     # Map components
│   └── forms/                    # Form components
├── lib/                          # Utilities & config
│   ├── firebase.ts               # Firebase configuration
│   ├── auth.ts                   # Auth helpers
│   ├── firestore.ts              # Database helpers
│   ├── validations.ts            # Zod schemas
│   └── utils.ts                  # Utility functions
├── hooks/                        # Custom React hooks
├── stores/                       # Zustand state management
└── types/                        # TypeScript definitions
    └── database.ts               # Database schemas
```

## 🔥 Firebase Configuration

### Required Environment Variables
```env
# Firebase
NEXT_PUBLIC_FIREBASE_API_KEY=
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=
NEXT_PUBLIC_FIREBASE_PROJECT_ID=
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=
NEXT_PUBLIC_FIREBASE_APP_ID=
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=

# Google Maps
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=

# External Services
GATETOPAY_API_KEY=
GATETOPAY_ENCRYPTION_KEY=
SMS_ACCOUNT_NAME=
SMS_ACCOUNT_PASSWORD=
SMS_BASE_URL=
```

## 🛠️ Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Phase Progress
- ✅ **Phase 1**: Project Setup (Completed)
- 🔄 **Phase 2**: Authentication (Next)
- ⏳ **Phase 3**: Database & Security
- ⏳ **Phase 4**: Core Features
- ⏳ **Phase 5**: Real-time Features
- ⏳ **Phase 6**: Admin Dashboard
- ⏳ **Phase 7**: Cloud Functions
- ⏳ **Phase 8**: Migration & Testing
- ⏳ **Phase 9**: Deployment

## 🎯 Key Features

### Core Functionality
- 📱 Multi-provider authentication (Phone, Google, Facebook, Apple)
- 🚗 Real-time driver tracking and management
- 📍 Order management with Google Maps integration
- 💳 Payment processing with GateToPay
- 📊 Comprehensive admin dashboard
- 🔔 Real-time notifications
- 📱 Mobile-responsive design

### Performance Targets
- Page Load Time: < 2 seconds
- API Response Time: < 500ms
- Real-time Updates: < 1 second latency
- Mobile Performance: 90+ Lighthouse score

## 📋 Implementation Checklist

Refer to `YELLOWTAXI_REBUILD_SPECIFICATION.md` for the complete implementation checklist and detailed specifications.

## 🚀 Deployment

### Firebase Hosting
```bash
# Build the project
npm run build

# Install Firebase CLI
npm install -g firebase-tools

# Login and deploy
firebase login
firebase deploy
```

## 📄 License

Private project - All rights reserved.

---

🚀 **Ready to revolutionize ride-sharing with modern web technologies!**